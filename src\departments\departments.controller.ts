import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  UseGuards,
  ParseU<PERSON><PERSON>ipe,
  BadRequestException,
} from '@nestjs/common';
import { DepartmentsService } from './departments.service';
import { CreateDepartmentDto, UpdateDepartmentDto, DepartmentResponseDto } from './dto/department.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth-guards';
import { RolesGuard } from '../auth/roles.guard';
import { Roles } from '../auth/roles.decorator';
import { CurrentUser } from '../auth/current-user-decorator';

@Controller('api/departments')
@UseGuards(JwtAuthGuard)
export class DepartmentsController {
  constructor(private readonly departmentsService: DepartmentsService) {}

  /**
   * Create new department
   * POST /api/departments
   * CompanyAdmin only
   */
  @Post()
  @UseGuards(RolesGuard)
  @Roles('CompanyAdmin')
  async createDepartment(
    @Body() createDepartmentDto: CreateDepartmentDto,
    @CurrentUser() user: any,
  ): Promise<DepartmentResponseDto> {
    try {
      // Set createdBy from current user
      createDepartmentDto.createdBy = user.sub;
      
      return await this.departmentsService.createDepartment(
        user.companyId,
        createDepartmentDto,
        user.sub,
      );
    } catch (error) {
      throw new BadRequestException(`Failed to create department: ${error.message}`);
    }
  }

  /**
   * Update department
   * PUT /api/departments/:id
   * CompanyAdmin only
   */
  @Put(':id')
  @UseGuards(RolesGuard)
  @Roles('CompanyAdmin')
  async updateDepartment(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateDepartmentDto: UpdateDepartmentDto,
    @CurrentUser() user: any,
  ): Promise<DepartmentResponseDto> {
    try {
      // Set updatedBy from current user
      updateDepartmentDto.updatedBy = user.sub;
      
      return await this.departmentsService.updateDepartment(
        id,
        user.companyId,
        updateDepartmentDto,
        user.sub,
      );
    } catch (error) {
      throw new BadRequestException(`Failed to update department: ${error.message}`);
    }
  }

  /**
   * Get all departments for the user's company
   * GET /api/departments
   * Any authenticated user
   */
  @Get()
  async getDepartments(@CurrentUser() user: any): Promise<DepartmentResponseDto[]> {
    try {
      return await this.departmentsService.getDepartmentsByCompany(user.companyId);
    } catch (error) {
      throw new BadRequestException(`Failed to get departments: ${error.message}`);
    }
  }

  /**
   * Get department by ID
   * GET /api/departments/:id
   * Any authenticated user
   */
  @Get(':id')
  async getDepartmentById(
    @Param('id', ParseUUIDPipe) id: string,
    @CurrentUser() user: any,
  ): Promise<DepartmentResponseDto> {
    try {
      return await this.departmentsService.getDepartmentById(id, user.companyId);
    } catch (error) {
      throw new BadRequestException(`Failed to get department: ${error.message}`);
    }
  }

  /**
   * Delete department
   * DELETE /api/departments/:id
   * CompanyAdmin only
   */
  @Delete(':id')
  @UseGuards(RolesGuard)
  @Roles('CompanyAdmin')
  async deleteDepartment(
    @Param('id', ParseUUIDPipe) id: string,
    @CurrentUser() user: any,
  ): Promise<{ message: string }> {
    try {
      return await this.departmentsService.deleteDepartment(id, user.companyId, user.sub);
    } catch (error) {
      throw new BadRequestException(`Failed to delete department: ${error.message}`);
    }
  }
}
