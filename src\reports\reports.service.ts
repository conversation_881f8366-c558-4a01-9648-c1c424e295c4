import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Sequelize } from 'sequelize-typescript';
import { Op, QueryTypes, CreationAttributes } from 'sequelize';
import { SurveyResponse } from '../database/models/survey-response.model';
import { AnswerCategory } from '../database/models/answer-category.model';
import { QuestionOption } from '../database/models/question-option.model';
import { Question } from '../database/models/question.model';
import { User } from '../database/models/user.model';
import { Company } from '../database/models/company.model';
import { Report, ReportType, ReportStatus, ReportFormat } from '../database/models/report.model';
import { CreateReportDto, UpdateReportDto, ReportQueryDto, GenerateReportDto } from './dto/report.dto';

@Injectable()
export class ReportsService {
  constructor(
    @InjectModel(SurveyResponse)
    private readonly surveyResponseModel: typeof SurveyResponse,
    @InjectModel(AnswerCategory)
    private readonly answerCategoryModel: typeof AnswerCategory,
    @InjectModel(QuestionOption)
    private readonly questionOptionModel: typeof QuestionOption,
    @InjectModel(Question)
    private readonly questionModel: typeof Question,
    @InjectModel(User)
    private readonly userModel: typeof User,
    @InjectModel(Company)
    private readonly companyModel: typeof Company,
    @InjectModel(Report)
    private readonly reportModel: typeof Report,
    private readonly sequelize: Sequelize,
  ) {}

  /**
   * Get comprehensive survey analytics for a company
   */
  async getCompanySurveyAnalytics(
    companyId: string,
    startDate: Date,
    endDate: Date,
    departmentFilter?: string
  ) {
    // Get company info
    const company = await this.companyModel.findByPk(companyId);
    if (!company) {
      throw new Error('Company not found');
    }

    // Build where clause for responses
    const whereClause: any = {
      company_id: companyId,
      response_date: { [Op.between]: [startDate, endDate] },
      isActive: true,
      isDeleted: false
    };

    if (departmentFilter) {
      whereClause.department = departmentFilter;
    }

    // Get total responses and response rate
    const totalResponses = await this.surveyResponseModel.count({ where: whereClause });
    const totalEmployees = await this.userModel.count({
      where: { company_id: companyId, isActive: true, isDeleted: false }
    });

    // Get category breakdown
    const categoryBreakdown = await this.getCategoryBreakdown(whereClause);

    // Get time series data
    const timeSeriesData = await this.getTimeSeriesData(companyId, startDate, endDate);

    // Get department breakdown
    const departmentBreakdown = await this.getDepartmentBreakdownData(companyId, startDate, endDate);

    // Get top questions analysis
    const topQuestions = await this.getTopQuestionsAnalysis(companyId, startDate, endDate);

    return {
      companyId,
      companyName: company.name,
      reportPeriod: { startDate, endDate },
      totalResponses,
      totalEmployees,
      responseRate: totalEmployees > 0 ? (totalResponses / totalEmployees) * 100 : 0,
      categoryBreakdown,
      timeSeriesData,
      departmentBreakdown,
      topQuestions
    };
  }

  /**
   * Get survey analytics for a supervisor's team
   */
  async getSupervisorTeamAnalytics(
    supervisorId: string,
    startDate: Date,
    endDate: Date
  ) {
    const whereClause = {
      supervisor_id: supervisorId,
      response_date: { [Op.between]: [startDate, endDate] },
      isActive: true,
      isDeleted: false
    };

    const totalResponses = await this.surveyResponseModel.count({ where: whereClause });
    const teamSize = await this.userModel.count({
      where: { supervisor_id: supervisorId, isActive: true, isDeleted: false }
    });

    const categoryBreakdown = await this.getCategoryBreakdown(whereClause);

    return {
      supervisorId,
      reportPeriod: { startDate, endDate },
      totalResponses,
      teamSize,
      responseRate: teamSize > 0 ? (totalResponses / teamSize) * 100 : 0,
      categoryBreakdown
    };
  }

  /**
   * Get category breakdown for responses
   */
  private async getCategoryBreakdown(whereClause: any) {
    const results = await this.surveyResponseModel.findAll({
      where: whereClause,
      include: [
        {
          model: QuestionOption,
          as: 'selectedOption',
          include: [
            {
              model: AnswerCategory,
              as: 'category'
            }
          ]
        }
      ]
    });

    const categoryStats = new Map();
    const totalResponses = results.length;

    // Initialize all categories
    const allCategories = await this.answerCategoryModel.findAll({
      where: { isActive: true, isDeleted: false },
      order: [['sort_order', 'ASC']]
    });

    allCategories.forEach(category => {
      categoryStats.set(category.id, {
        categoryId: category.id,
        categoryName: category.name,
        displayName: category.display_name,
        colorCode: category.color_code,
        responseCount: 0,
        percentage: 0,
        trend: 0 // TODO: Calculate trend
      });
    });

    // Count responses by category
    results.forEach(response => {
      const categoryId = response.selectedOption?.category?.id;
      if (categoryId && categoryStats.has(categoryId)) {
        const stats = categoryStats.get(categoryId);
        stats.responseCount++;
        stats.percentage = totalResponses > 0 ? (stats.responseCount / totalResponses) * 100 : 0;
      }
    });

    return Array.from(categoryStats.values());
  }

  /**
   * Get time series data for trend analysis
   */
  private async getTimeSeriesData(companyId: string, startDate: Date, endDate: Date) {
    // This is a simplified version - you might want to group by week/month
    const query = `
      SELECT 
        DATE(response_date) as date,
        ac.name as category,
        COUNT(*) as count
      FROM SurveyResponses sr
      JOIN QuestionOptions qo ON sr.selected_option_id = qo.id
      JOIN AnswerCategories ac ON qo.category_id = ac.id
      WHERE sr.company_id = :companyId 
        AND sr.response_date BETWEEN :startDate AND :endDate
        AND sr.isActive = 1 
        AND sr.isDeleted = 0
      GROUP BY DATE(response_date), ac.name, ac.sort_order
      ORDER BY DATE(response_date), ac.sort_order
    `;

    const results = await this.sequelize.query(query, {
      replacements: { companyId, startDate, endDate },
      type: QueryTypes.SELECT
    });

    // Transform results into time series format
    const timeSeriesMap = new Map();
    
    results.forEach((row: any) => {
      if (!timeSeriesMap.has(row.date)) {
        timeSeriesMap.set(row.date, {
          date: row.date,
          VERY_POSITIVE: 0,
          POSITIVE: 0,
          NEUTRAL: 0,
          NEGATIVE: 0,
          VERY_NEGATIVE: 0
        });
      }
      timeSeriesMap.get(row.date)[row.category] = row.count;
    });

    return Array.from(timeSeriesMap.values());
  }

  /**
   * Get department breakdown data
   */
  private async getDepartmentBreakdownData(companyId: string, startDate: Date, endDate: Date) {
    const query = `
      SELECT 
        sr.department,
        ac.name as category,
        COUNT(*) as count
      FROM SurveyResponses sr
      JOIN QuestionOptions qo ON sr.selected_option_id = qo.id
      JOIN AnswerCategories ac ON qo.category_id = ac.id
      WHERE sr.company_id = :companyId 
        AND sr.response_date BETWEEN :startDate AND :endDate
        AND sr.isActive = 1 
        AND sr.isDeleted = 0
        AND sr.department IS NOT NULL
      GROUP BY sr.department, ac.name
      ORDER BY sr.department, ac.sort_order
    `;

    const results = await this.sequelize.query(query, {
      replacements: { companyId, startDate, endDate },
      type: QueryTypes.SELECT
    });

    // Transform results
    const departmentMap = new Map();
    
    results.forEach((row: any) => {
      if (!departmentMap.has(row.department)) {
        departmentMap.set(row.department, {
          departmentName: row.department,
          totalResponses: 0,
          categories: {
            VERY_POSITIVE: 0,
            POSITIVE: 0,
            NEUTRAL: 0,
            NEGATIVE: 0,
            VERY_NEGATIVE: 0
          }
        });
      }
      const dept = departmentMap.get(row.department);
      dept.categories[row.category] = row.count;
      dept.totalResponses += row.count;
    });

    return Array.from(departmentMap.values());
  }

  /**
   * Get top questions analysis
   */
  private async getTopQuestionsAnalysis(companyId: string, startDate: Date, endDate: Date) {
    const query = `
      SELECT 
        q.id as questionId,
        q.question_text,
        ac.name as category,
        COUNT(*) as count
      FROM SurveyResponses sr
      JOIN Questions q ON sr.question_id = q.id
      JOIN QuestionOptions qo ON sr.selected_option_id = qo.id
      JOIN AnswerCategories ac ON qo.category_id = ac.id
      WHERE sr.company_id = :companyId 
        AND sr.response_date BETWEEN :startDate AND :endDate
        AND sr.isActive = 1 
        AND sr.isDeleted = 0
      GROUP BY q.id, q.question_text, ac.name
      ORDER BY q.id, ac.sort_order
    `;

    const results = await this.sequelize.query(query, {
      replacements: { companyId, startDate, endDate },
      type: QueryTypes.SELECT
    });

    // Transform results
    const questionMap = new Map();
    
    results.forEach((row: any) => {
      if (!questionMap.has(row.questionId)) {
        questionMap.set(row.questionId, {
          questionId: row.questionId,
          questionText: row.question_text,
          totalResponses: 0,
          categoryDistribution: {
            VERY_POSITIVE: 0,
            POSITIVE: 0,
            NEUTRAL: 0,
            NEGATIVE: 0,
            VERY_NEGATIVE: 0
          }
        });
      }
      const question = questionMap.get(row.questionId);
      question.categoryDistribution[row.category] = row.count;
      question.totalResponses += row.count;
    });

    return Array.from(questionMap.values()).slice(0, 10); // Top 10 questions
  }

  // Additional methods for other endpoints...
  async getQuestionCategoryBreakdown(questionId: string, companyId?: string, startDate?: Date, endDate?: Date) {
    // Implementation for question-specific breakdown
    // ... (implement based on requirements)
  }

  async getTrendAnalysis(companyId: string, startDate: Date, endDate: Date, interval: string) {
    // Implementation for trend analysis
    // ... (implement based on requirements)
  }

  async getDepartmentAnalytics(companyId: string, startDate: Date, endDate: Date) {
    // Implementation for department analytics
    // ... (implement based on requirements)
  }

  async exportReportData(companyId: string, format: string, startDate: Date, endDate: Date) {
    // Implementation for export functionality
    // ... (implement based on requirements)
  }

  // Report Management Methods

  /**
   * Create a new report record
   */
  async createReport(createReportDto: CreateReportDto): Promise<Report> {
    const reportData = {
      ...createReportDto,
      start_date: new Date(createReportDto.start_date),
      end_date: new Date(createReportDto.end_date),
      status: ReportStatus.PENDING,
      // Set expiration to 30 days from now by default
      expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
    };

    return this.reportModel.create(reportData as CreationAttributes<Report>);
  }

  /**
   * Get all reports with optional filtering
   */
  async getReports(queryDto: ReportQueryDto): Promise<{ reports: Report[]; total: number }> {
    const whereClause: any = {
      isDeleted: false
    };

    if (queryDto.company_id) {
      whereClause.company_id = queryDto.company_id;
    }

    if (queryDto.type) {
      whereClause.type = queryDto.type;
    }

    if (queryDto.status) {
      whereClause.status = queryDto.status;
    }

    if (queryDto.format) {
      whereClause.format = queryDto.format;
    }

    if (queryDto.requested_by) {
      whereClause.requested_by = queryDto.requested_by;
    }

    if (queryDto.start_date && queryDto.end_date) {
      whereClause.start_date = {
        [Op.between]: [new Date(queryDto.start_date), new Date(queryDto.end_date)]
      };
    }

    const { count, rows } = await this.reportModel.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: Company,
          attributes: ['id', 'name']
        },
        {
          model: User,
          as: 'requestedBy',
          attributes: ['id', 'firstName', 'lastName', 'email']
        }
      ],
      order: [['createdAt', 'DESC']],
      limit: queryDto.limit || 10,
      offset: queryDto.offset || 0
    });

    return {
      reports: rows,
      total: count
    };
  }

  /**
   * Get a specific report by ID
   */
  async getReportById(id: string): Promise<Report> {
    const report = await this.reportModel.findOne({
      where: { id, isDeleted: false },
      include: [
        {
          model: Company,
          attributes: ['id', 'name']
        },
        {
          model: User,
          as: 'requestedBy',
          attributes: ['id', 'firstName', 'lastName', 'email']
        }
      ]
    });

    if (!report) {
      throw new NotFoundException(`Report with ID ${id} not found`);
    }

    return report;
  }

  /**
   * Update a report
   */
  async updateReport(id: string, updateReportDto: UpdateReportDto): Promise<Report> {
    const report = await this.getReportById(id);

    const updateData: any = { ...updateReportDto };

    // Convert date strings to Date objects
    if (updateReportDto.processing_started_at) {
      updateData.processing_started_at = new Date(updateReportDto.processing_started_at);
    }
    if (updateReportDto.processing_completed_at) {
      updateData.processing_completed_at = new Date(updateReportDto.processing_completed_at);
    }
    if (updateReportDto.expires_at) {
      updateData.expires_at = new Date(updateReportDto.expires_at);
    }

    await report.update(updateData);
    return this.getReportById(id);
  }

  /**
   * Delete a report (soft delete)
   */
  async deleteReport(id: string, userId: string): Promise<{ message: string }> {
    const report = await this.getReportById(id);

    await report.update({
      isDeleted: true,
      deletedBy: userId
    });

    return { message: 'Report deleted successfully' };
  }

  /**
   * Generate and store a report
   */
  async generateAndStoreReport(generateReportDto: GenerateReportDto, userId: string): Promise<Report> {
    const transaction = await this.sequelize.transaction();

    try {
      // Create report record
      const reportName = generateReportDto.name ||
        `${generateReportDto.type.replace(/_/g, ' ').toUpperCase()} - ${new Date().toISOString().split('T')[0]}`;

      const report = await this.reportModel.create({
        name: reportName,
        type: generateReportDto.type,
        format: generateReportDto.format || ReportFormat.JSON,
        company_id: generateReportDto.company_id,
        requested_by: userId,
        start_date: new Date(generateReportDto.start_date),
        end_date: new Date(generateReportDto.end_date),
        parameters: generateReportDto.parameters || {},
        status: ReportStatus.PROCESSING,
        processing_started_at: new Date(),
        expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
        createdBy: userId
      } as CreationAttributes<Report>, { transaction });

      // Generate the actual report data based on type
      let reportData: any;
      const startDate = new Date(generateReportDto.start_date);
      const endDate = new Date(generateReportDto.end_date);

      try {
        switch (generateReportDto.type) {
          case ReportType.COMPANY_SURVEY_ANALYTICS:
            reportData = await this.getCompanySurveyAnalytics(
              generateReportDto.company_id,
              startDate,
              endDate,
              generateReportDto.parameters?.departmentFilter
            );
            break;

          case ReportType.TREND_ANALYSIS:
            reportData = await this.getTimeSeriesData(
              generateReportDto.company_id,
              startDate,
              endDate
            );
            break;

          case ReportType.DEPARTMENT_ANALYTICS:
            reportData = await this.getDepartmentBreakdownData(
              generateReportDto.company_id,
              startDate,
              endDate
            );
            break;

          default:
            throw new BadRequestException(`Report type ${generateReportDto.type} not implemented`);
        }

        // Update report with generated data
        await report.update({
          data: reportData,
          status: ReportStatus.COMPLETED,
          processing_completed_at: new Date()
        }, { transaction });

      } catch (error) {
        // Update report with error status
        await report.update({
          status: ReportStatus.FAILED,
          error_message: error.message,
          processing_completed_at: new Date()
        }, { transaction });

        throw error;
      }

      await transaction.commit();
      return this.getReportById(report.id);

    } catch (error) {
      await transaction.rollback();
      throw new BadRequestException(`Failed to generate report: ${error.message}`);
    }
  }

  /**
   * Clean up expired reports
   */
  async cleanupExpiredReports(): Promise<{ deletedCount: number }> {
    const expiredReports = await this.reportModel.findAll({
      where: {
        expires_at: {
          [Op.lt]: new Date()
        },
        isDeleted: false
      }
    });

    const deletedCount = expiredReports.length;

    if (deletedCount > 0) {
      await this.reportModel.update(
        { isDeleted: true },
        {
          where: {
            id: {
              [Op.in]: expiredReports.map(report => report.id)
            }
          }
        }
      );
    }

    return { deletedCount };
  }
}
