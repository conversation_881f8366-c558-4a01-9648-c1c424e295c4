import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { ProductOwnerController } from './product-owner.controller';
import { ProductOwnerService } from './product-owner.service';
import { MailServiceService } from 'src/mail-service/mail-service.service';
import { ProductOwner } from 'src/database/models/product-owner-model';
import { Company } from 'src/database/models/company.model';
import { User } from 'src/database/models/user.model';
import { Role } from 'src/database/models/role.model';
import { Question } from 'src/database/models/question.model';
import { QuestionOption } from 'src/database/models/question-option.model';
import { CompanyQuestionMap } from 'src/database/models/company-question-map.model';
import { QuestionService } from './question.service';
import { ProductOwnerQuestionController, CompanyQuestionController } from './question.controller';

@Module({
  imports: [
    SequelizeModule.forFeature([
      ProductOwner, 
      Company, 
      User, 
      Role, 
      Question, 
      QuestionOption, 
      CompanyQuestionMap
    ])
  ],
  controllers: [
    ProductOwnerController, 
    ProductOwnerQuestionController, 
    CompanyQuestionController
  ],
  providers: [
    ProductOwnerService, 
    MailServiceService, 
    QuestionService
  ],
})
export class ProductOwnerModule {}

