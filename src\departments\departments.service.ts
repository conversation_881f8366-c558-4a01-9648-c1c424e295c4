import { Injectable, NotFoundException, BadRequestException, ForbiddenException } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { CreationAttributes, Op } from 'sequelize';
import { Department } from '../database/models/department.model';
import { Company } from '../database/models/company.model';
import { CreateDepartmentDto, UpdateDepartmentDto, DepartmentResponseDto } from './dto/department.dto';

@Injectable()
export class DepartmentsService {
  constructor(
    @InjectModel(Department)
    private readonly departmentModel: typeof Department,
    @InjectModel(Company)
    private readonly companyModel: typeof Company,
  ) {}

  /**
   * Create a new department (CompanyAdmin only)
   */
  async createDepartment(
    companyId: string,
    dto: CreateDepartmentDto,
    userId: string,
  ): Promise<DepartmentResponseDto> {
    // Verify company exists
    const company = await this.companyModel.findOne({
      where: { id: companyId, isDeleted: false },
    });

    if (!company) {
      throw new NotFoundException('Company not found');
    }

    // Check if department name already exists in the company
    const existingDepartment = await this.departmentModel.findOne({
      where: {
        name: dto.name,
        company_id: companyId,
        isDeleted: false,
      },
    });

    if (existingDepartment) {
      throw new BadRequestException(`Department with name '${dto.name}' already exists in this company`);
    }

    // Create department
    const department = await this.departmentModel.create({
      ...dto,
      company_id: companyId,
      createdBy: userId,
    } as CreationAttributes<Department>);

    return this.mapToResponseDto(department);
  }

  /**
   * Update department (CompanyAdmin only)
   */
  async updateDepartment(
    id: string,
    companyId: string,
    dto: UpdateDepartmentDto,
    userId: string,
  ): Promise<DepartmentResponseDto> {
    const department = await this.departmentModel.findOne({
      where: { id, isDeleted: false },
    });

    if (!department) {
      throw new NotFoundException('Department not found');
    }

    // Verify department belongs to the company
    if (department.company_id !== companyId) {
      throw new ForbiddenException('You can only update departments within your company');
    }

    // If updating name, check for duplicates
    if (dto.name && dto.name !== department.name) {
      const existingDepartment = await this.departmentModel.findOne({
        where: {
          name: dto.name,
          company_id: companyId,
          isDeleted: false,
          id: { [Op.ne]: id },
        },
      });

      if (existingDepartment) {
        throw new BadRequestException(`Department with name '${dto.name}' already exists in this company`);
      }
    }

    // Update department
    await department.update({
      ...dto,
      updatedBy: userId,
    });

    return this.mapToResponseDto(department);
  }

  /**
   * Get departments by company (any authenticated user)
   */
  async getDepartmentsByCompany(companyId: string): Promise<DepartmentResponseDto[]> {
    const departments = await this.departmentModel.findAll({
      where: {
        company_id: companyId,
        isDeleted: false,
      },
      order: [['name', 'ASC']],
    });

    console.log('Departments found:', departments);

    return departments.map(dept => this.mapToResponseDto(dept));
  }

  /**
   * Get department by ID (any authenticated user)
   */
  async getDepartmentById(id: string, companyId: string): Promise<DepartmentResponseDto> {
    const department = await this.departmentModel.findOne({
      where: {
        id,
        company_id: companyId,
        isDeleted: false,
      },
    });

    if (!department) {
      throw new NotFoundException('Department not found');
    }

    return this.mapToResponseDto(department);
  }

  /**
   * Delete department (CompanyAdmin only)
   */
  async deleteDepartment(id: string, companyId: string, userId: string): Promise<{ message: string }> {
    const department = await this.departmentModel.findOne({
      where: { id, isDeleted: false },
    });

    if (!department) {
      throw new NotFoundException('Department not found');
    }

    // Verify department belongs to the company
    if (department.company_id !== companyId) {
      throw new ForbiddenException('You can only delete departments within your company');
    }

    // Soft delete
    await department.update({
      isDeleted: true,
      deletedBy: userId,
    });

    return { message: 'Department deleted successfully' };
  }

  /**
   * Map Department model to response DTO
   */
  private mapToResponseDto(department: Department): DepartmentResponseDto {
    return {
      id: department.id,
      name: department.name,
      description: department.description,
      company_id: department.company_id,
      isActive: department.isActive,
      createdAt: department.createdAt,
      updatedAt: department.updatedAt,
      createdBy: department.createdBy,
      updatedBy: department.updatedBy,
    };
  }
}
