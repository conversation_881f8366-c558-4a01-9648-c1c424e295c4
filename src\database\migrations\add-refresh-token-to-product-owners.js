'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    console.log('🚀 Starting migration: Adding refreshToken to ProductOwners table...');
    
    try {
      // Check if refreshToken column already exists
      const tableDescription = await queryInterface.describeTable('ProductOwners');
      
      if (tableDescription.refreshToken) {
        console.log('⚠️ refreshToken column already exists, skipping column creation');
        return;
      }

      // Add refreshToken column to ProductOwners table
      await queryInterface.addColumn('ProductOwners', 'refreshToken', {
        type: Sequelize.STRING,
        allowNull: true,
        comment: 'JWT refresh token for authentication'
      });
      
      console.log('✅ refreshToken column added to ProductOwners table');
      console.log('🎉 Migration completed: ProductOwners table updated!');
    } catch (error) {
      console.error('❌ Error during ProductOwners migration:', error);
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    console.log('🔄 Rolling back migration: Removing refreshToken from ProductOwners...');
    
    try {
      // Check if column exists before trying to remove it
      const tableDescription = await queryInterface.describeTable('ProductOwners');
      
      if (tableDescription.refreshToken) {
        await queryInterface.removeColumn('ProductOwners', 'refreshToken');
        console.log('✅ refreshToken column removed from ProductOwners');
      } else {
        console.log('⚠️ refreshToken column does not exist, nothing to remove');
      }
      
      console.log('🔙 Migration rollback completed');
    } catch (error) {
      console.error('❌ Error during rollback:', error);
      throw error;
    }
  }
};
