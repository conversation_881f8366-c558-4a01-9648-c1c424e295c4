// src/product-owner/product-owner.controller.ts
import { Body, Controller, Get, Param, ParseUUIDPipe, Post, UseGuards } from '@nestjs/common';
import { ProductOwnerService } from './product-owner.service';
import { CreateCompanyDto } from './dto/create-company-dto';
import { JwtAuthGuard } from 'src/auth/guards/jwt-auth-guards';
import { RolesGuard } from 'src/auth/roles.guard';
import { CurrentUser } from 'src/auth/current-user-decorator';
import { User } from 'src/database/models/user.model';
import { Roles } from 'src/auth/roles.decorator';

@Controller('product-owner')
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles('ProductOwner')
export class ProductOwnerController {
    constructor(private readonly productOwnerService: ProductOwnerService) { }

    @Post('companies')
    @Roles('ProductOwner') // allow both
    async createCompany(@Body() dto: CreateCompanyDto, @CurrentUser() user: User) {
        console.log('Creating company by:', user.id);
        return this.productOwnerService.createCompany(dto);
    }

    @Get('companies')
    @Roles('ProductOwner')
    async getCompanies(@CurrentUser() user: User) {
        return this.productOwnerService.getCompanies();
    }

    @Get('companies/:id')
    @Roles('ProductOwner')
    async getCompanyById(@Param('id', ParseUUIDPipe) id: string, @CurrentUser() user: User) {
        return this.productOwnerService.getCompanyById(id);
    }
}
