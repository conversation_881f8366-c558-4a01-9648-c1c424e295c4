import { 
  Controller, 
  Get, 
  Post, 
  Put, 
  Delete, 
  Body, 
  Param, 
  UseGuards, 
  BadRequestException,
  ParseUUIDPipe
} from '@nestjs/common';
import { QuestionService } from './question.service';
import { CreateQuestionDto } from './dto/create-question.dto';
import { UpdateQuestionDto } from './dto/update-question.dto';
import { CreateQuestionOptionDto } from './dto/create-question-option.dto';
import { UpdateQuestionOptionDto } from './dto/update-question-option.dto';
import { AssignCompanyQuestionDto } from './dto/assign-company-question.dto';
import { JwtAuthGuard } from 'src/auth/guards/jwt-auth-guards';
import { RolesGuard } from 'src/auth/roles.guard';
import { Roles } from 'src/auth/roles.decorator';
import { CurrentUser } from 'src/auth/current-user-decorator';

@Controller('product-owner/questions')
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles('ProductOwner') // Class-level role restriction
export class ProductOwnerQuestionController {
  constructor(private readonly questionService: QuestionService) {}

  // Question endpoints
  @Get()
  async getAllQuestions() {
    return this.questionService.getAllQuestions();
  }

  @Get(':id')
  async getQuestionById(@Param('id', ParseUUIDPipe) id: string) {
    return this.questionService.getQuestionById(id);
  }
  
  @Post()
  @Roles('ProductOwner') // Method-level role restriction (redundant but explicit)
  async createQuestion(
    @Body() createQuestionDto: CreateQuestionDto,
    @CurrentUser() user: any
  ) {
    console.log('Creating question with user role:', user.role);
    // Add the current user's ID as the creator
    createQuestionDto.createdBy = user.userId;
    return this.questionService.createQuestion(createQuestionDto);
  }

  @Put(':id')
  async updateQuestion(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateQuestionDto: UpdateQuestionDto,
    @CurrentUser() user: any
  ) {
    updateQuestionDto.updatedBy = user.userId;
    return this.questionService.updateQuestion(id, updateQuestionDto);
  }

  @Delete(':id')
  async deleteQuestion(
    @Param('id', ParseUUIDPipe) id: string,
    @CurrentUser() user: any
  ) {
    return this.questionService.deleteQuestion(id, user.userId);
  }

  // Question Option endpoints
  @Post(':questionId/options')
  async addQuestionOption(
    @Param('questionId', ParseUUIDPipe) questionId: string,
    @Body() createOptionDto: CreateQuestionOptionDto,
    @CurrentUser() user: any
  ) {
    createOptionDto.createdBy = user.userId;
    return this.questionService.addQuestionOption(questionId, createOptionDto);
  }

  @Put(':questionId/options/:optionId')
  async updateQuestionOption(
    @Param('questionId', ParseUUIDPipe) questionId: string,
    @Param('optionId', ParseUUIDPipe) optionId: string,
    @Body() updateOptionDto: UpdateQuestionOptionDto,
    @CurrentUser() user: any
  ) {
    updateOptionDto.updatedBy = user.userId;
    return this.questionService.updateQuestionOption(questionId, optionId, updateOptionDto);
  }

  @Delete(':questionId/options/:optionId')
  async deleteQuestionOption(
    @Param('questionId', ParseUUIDPipe) questionId: string,
    @Param('optionId', ParseUUIDPipe) optionId: string,
    @CurrentUser() user: any
  ) {
    return this.questionService.deleteQuestionOption(questionId, optionId, user.userId);
  }
}

@Controller('product-owner/companies/:companyId/questions')
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles('ProductOwner')
export class CompanyQuestionController {
  constructor(private readonly questionService: QuestionService) {}

  @Get()
  async getCompanyQuestions(
    @Param('companyId', ParseUUIDPipe) companyId: string
  ) {
    return this.questionService.getCompanyQuestions(companyId);
  }

  @Post()
  async assignQuestionsToCompany(
    @Param('companyId', ParseUUIDPipe) companyId: string,
    @Body() assignDto: AssignCompanyQuestionDto,
    @CurrentUser() user: any
  ) {
    assignDto.createdBy = user.userId;
    return this.questionService.assignQuestionsToCompany(companyId, assignDto);
  }

  @Delete(':questionId')
  async removeQuestionFromCompany(
    @Param('companyId', ParseUUIDPipe) companyId: string,
    @Param('questionId', ParseUUIDPipe) questionId: string,
    @CurrentUser() user: any
  ) {
    return this.questionService.removeQuestionFromCompany(companyId, questionId, user.userId);
  }
}
