import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  ParseUUIDPipe,
  BadRequestException,
  ParseBoolPipe,
  ParseIntPipe,
} from '@nestjs/common';
import { UsersService } from './users.service';
import { CreateUserDto, UpdateUserDto, UserResponseDto, UserListResponseDto } from './dto/user.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth-guards';
import { RolesGuard } from '../auth/roles.guard';
import { Roles } from '../auth/roles.decorator';
import { CurrentUser } from '../auth/current-user-decorator';

@Controller('api/users')
@UseGuards(JwtAuthGuard)
export class UsersController {
  constructor(private readonly usersService: UsersService) { }

  /**
   * Create new user
   * POST /api/users
   * CompanyAdmin only
   */
  @Post()
  @UseGuards(RolesGuard)
  @Roles('CompanyAdmin')
  async createUser(
    @Body() createUserDto: CreateUserDto,
    @CurrentUser() user: any,
  ): Promise<UserResponseDto> {
    try {
      if (!user.companyId) {
        throw new BadRequestException('User does not have a company assigned');
      }

      // Set createdBy from current user
      createUserDto.createdBy = user.sub;

      return await this.usersService.createUser(
        user.companyId,
        createUserDto,
        user.sub,
      );
    } catch (error) {
      throw new BadRequestException(`Failed to create user: ${error.message}`);
    }
  }

  /**
   * Get all users
   * GET /api/users
   * CompanyAdmin can see all, Supervisor can see their team, Employee can see themselves
   */
  @Get()
  async getUsers(
    @CurrentUser() user: any,
    @Query('role') role?: string,
    @Query('department') department?: string,
    @Query('isActive', new ParseBoolPipe({ optional: true })) isActive?: boolean,
    @Query('page', new ParseIntPipe({ optional: true })) page?: number,
    @Query('limit', new ParseIntPipe({ optional: true })) limit?: number,
  ): Promise<UserListResponseDto> {
    try {
      if (!user.companyId) {
        throw new BadRequestException('User does not have a company assigned');
      }

      const filters = {
        role,
        department,
        isActive,
        page,
        limit,
      };

      return await this.usersService.getUsersByCompany(
        user.companyId,
        user.role,
        user.sub,
        filters,
      );


    } catch (error) {
      throw new BadRequestException(`Failed to get users: ${error.message}`);
    }
  }

  /**
   * Get user by ID
   * GET /api/users/:id
   * Role-based access control applied
   */
  @Get(':id')
  async getUserById(
    @Param('id', ParseUUIDPipe) id: string,
    @CurrentUser() user: any,
  ): Promise<UserResponseDto> {
    try {
      if (!user.companyId) {
        throw new BadRequestException('User does not have a company assigned');
      }

      return await this.usersService.getUserById(
        id,
        user.companyId,
        user.role,
        user.sub,
      );
    } catch (error) {
      throw new BadRequestException(`Failed to get user: ${error.message}`);
    }
  }

  /**
   * Update user
   * PUT /api/users/:id
   * CompanyAdmin only
   */
  @Put(':id')
  @UseGuards(RolesGuard)
  @Roles('CompanyAdmin')
  async updateUser(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateUserDto: UpdateUserDto,
    @CurrentUser() user: any,
  ): Promise<UserResponseDto> {
    try {
      if (!user.companyId) {
        throw new BadRequestException('User does not have a company assigned');
      }

      // Set updatedBy from current user
      updateUserDto.updatedBy = user.sub;

      return await this.usersService.updateUser(
        id,
        user.companyId,
        updateUserDto,
        user.sub,
      );
    } catch (error) {
      throw new BadRequestException(`Failed to update user: ${error.message}`);
    }
  }

  /**
   * Delete user
   * DELETE /api/users/:id
   * CompanyAdmin only
   */
  @Delete(':id')
  @UseGuards(RolesGuard)
  @Roles('CompanyAdmin')
  async deleteUser(
    @Param('id', ParseUUIDPipe) id: string,
    @CurrentUser() user: any,
  ): Promise<{ message: string }> {
    try {
      if (!user.companyId) {
        throw new BadRequestException('User does not have a company assigned');
      }

      return await this.usersService.deleteUser(
        id,
        user.companyId,
        user.sub,
      );
    } catch (error) {
      throw new BadRequestException(`Failed to delete user: ${error.message}`);
    }
  }
}
