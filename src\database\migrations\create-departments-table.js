'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('Departments', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.literal('(NEWID())'),
        primaryKey: true,
        allowNull: false
      },
      name: {
        type: Sequelize.STRING(150),
        allowNull: false
      },
      description: {
        type: Sequelize.STRING(500),
        allowNull: true
      },
      company_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'Companies',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'NO ACTION'
      },
      isActive: {
        type: Sequelize.BOOLEAN,
        defaultValue: true
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('GETDATE()')
      },
      createdBy: {
        type: Sequelize.UUID,
        allowNull: true
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('GETDATE()')
      },
      updatedBy: {
        type: Sequelize.UUID,
        allowNull: true
      },
      isDeleted: {
        type: Sequelize.BOOLEAN,
        defaultValue: false
      },
      deletedBy: {
        type: Sequelize.UUID,
        allowNull: true
      }
    });

    // Add indexes for performance
    await queryInterface.addIndex('Departments', ['company_id'], {
      name: 'idx_departments_company_id'
    });

    await queryInterface.addIndex('Departments', ['company_id', 'isDeleted'], {
      name: 'idx_departments_company_active'
    });

    await queryInterface.addIndex('Departments', ['name', 'company_id'], {
      name: 'idx_departments_name_company',
      unique: true,
      where: {
        isDeleted: false
      }
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('Departments');
  }
};
