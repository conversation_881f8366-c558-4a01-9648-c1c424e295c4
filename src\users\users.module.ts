import { <PERSON>du<PERSON> } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { UsersController } from './users.controller';
import { UsersService } from './users.service';
import { User } from '../database/models/user.model';
import { Role } from '../database/models/role.model';
import { Company } from '../database/models/company.model';
import { Department } from '../database/models/department.model';
import { EmailService } from '../common/services/email.service';

@Module({
  imports: [
    SequelizeModule.forFeature([User, Role, Company, Department]),
  ],
  controllers: [UsersController],
  providers: [UsersService, EmailService],
  exports: [UsersService, EmailService],
})
export class UsersModule {}
