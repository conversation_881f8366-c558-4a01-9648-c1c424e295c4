'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    console.log('🚀 Starting migration: Creating SurveyResponses table...');

    try {
      // Check if table already exists
      const tableExists = await queryInterface.sequelize.query(`
        SELECT COUNT(*) as count
        FROM INFORMATION_SCHEMA.TABLES
        WHERE TABLE_NAME = 'SurveyResponses'
      `, { type: Sequelize.QueryTypes.SELECT });

      if (tableExists[0].count > 0) {
        console.log('⚠️ SurveyResponses table already exists, skipping creation');
        return;
      }

      // Create table without foreign keys first
      await queryInterface.createTable('SurveyResponses', {
        id: {
          type: Sequelize.UUID,
          defaultValue: Sequelize.UUIDV4,
          primaryKey: true,
          allowNull: false
        },
        user_id: {
          type: Sequelize.UUID,
          allowNull: false
        },
        company_id: {
          type: Sequelize.UUID,
          allowNull: false
        },
        question_id: {
          type: Sequelize.UUID,
          allowNull: false
        },
        selected_option_id: {
          type: Sequelize.UUID,
          allowNull: false
        },
        survey_session_id: {
          type: Sequelize.UUID,
          allowNull: false
        },
        response_date: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
        },
        supervisor_id: {
          type: Sequelize.UUID,
          allowNull: true
        },
        department: {
          type: Sequelize.STRING(100),
          allowNull: true
        },
        isActive: {
          type: Sequelize.BOOLEAN,
          defaultValue: true
        },
        createdAt: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
        },
        createdBy: {
          type: Sequelize.UUID,
          allowNull: true
        },
        updatedAt: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
        },
        updatedBy: {
          type: Sequelize.UUID,
          allowNull: true
        },
        isDeleted: {
          type: Sequelize.BOOLEAN,
          defaultValue: false
        },
        deletedBy: {
          type: Sequelize.UUID,
          allowNull: true
        }
      });

      console.log('✅ SurveyResponses table created successfully');

      // Add foreign key constraints separately
      console.log('🔧 Adding foreign key constraints...');

      try {
        // Add foreign key constraints one by one
        await queryInterface.addConstraint('SurveyResponses', {
          fields: ['user_id'],
          type: 'foreign key',
          name: 'fk_survey_responses_user',
          references: {
            table: 'Users',
            field: 'id'
          },
          onDelete: 'CASCADE',
          onUpdate: 'CASCADE'
        });

        await queryInterface.addConstraint('SurveyResponses', {
          fields: ['company_id'],
          type: 'foreign key',
          name: 'fk_survey_responses_company',
          references: {
            table: 'Companies',
            field: 'id'
          },
          onDelete: 'CASCADE',
          onUpdate: 'CASCADE'
        });

        await queryInterface.addConstraint('SurveyResponses', {
          fields: ['question_id'],
          type: 'foreign key',
          name: 'fk_survey_responses_question',
          references: {
            table: 'Questions',
            field: 'id'
          },
          onDelete: 'CASCADE',
          onUpdate: 'CASCADE'
        });

        await queryInterface.addConstraint('SurveyResponses', {
          fields: ['selected_option_id'],
          type: 'foreign key',
          name: 'fk_survey_responses_option',
          references: {
            table: 'QuestionOptions',
            field: 'id'
          },
          onDelete: 'CASCADE',
          onUpdate: 'CASCADE'
        });

        await queryInterface.addConstraint('SurveyResponses', {
          fields: ['supervisor_id'],
          type: 'foreign key',
          name: 'fk_survey_responses_supervisor',
          references: {
            table: 'Users',
            field: 'id'
          },
          onDelete: 'SET NULL',
          onUpdate: 'CASCADE'
        });

        console.log('✅ Foreign key constraints added successfully');
      } catch (fkError) {
        console.log('⚠️ Some foreign key constraints may not have been created:', fkError.message);
      }

      // Add basic indexes
      console.log('🔧 Adding basic indexes...');

      try {
        await queryInterface.addIndex('SurveyResponses', ['company_id'], {
          name: 'idx_survey_responses_company'
        });

        await queryInterface.addIndex('SurveyResponses', ['user_id'], {
          name: 'idx_survey_responses_user'
        });

        await queryInterface.addIndex('SurveyResponses', ['survey_session_id'], {
          name: 'idx_survey_responses_session'
        });

        console.log('✅ Basic indexes added successfully');
      } catch (indexError) {
        console.log('⚠️ Some indexes may not have been created:', indexError.message);
      }

      console.log('🎉 Migration completed: SurveyResponses table is ready!');
    } catch (error) {
      console.error('❌ Error during SurveyResponses migration:', error);
      throw error;
    }
  },

  down: async (queryInterface) => {
    console.log('🔄 Rolling back migration: Dropping SurveyResponses table...');
    await queryInterface.dropTable('SurveyResponses');
    console.log('✅ SurveyResponses table dropped successfully');
    console.log('🔙 Migration rollback completed');
  }
};
