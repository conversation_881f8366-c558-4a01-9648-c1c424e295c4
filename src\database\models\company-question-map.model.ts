import { Table, Column, Model, DataType, ForeignKey, BelongsTo, <PERSON><PERSON>ult, PrimaryKey, AllowNull, CreatedAt, UpdatedAt } from 'sequelize-typescript';
import { Question } from './question.model';
import { Company } from './company.model';

@Table({
  tableName: 'CompanyQuestionMaps',
  timestamps: true
})
export class CompanyQuestionMap extends Model<CompanyQuestionMap> {
  @PrimaryKey
  @Default(DataType.UUIDV4)
  @Column({ type: DataType.UUID })
  declare id: string;

  @ForeignKey(() => Company)
  @AllowNull(false)
  @Column(DataType.UUID)
  declare company_id: string;

  @ForeignKey(() => Question)
  @AllowNull(false)
  @Column(DataType.UUID)
  declare question_id: string;

  @Default(true)
  @Column(DataType.BOOLEAN)
  declare isActive: boolean;

  @CreatedAt
  @Column(DataType.DATE)
  declare createdAt: Date;

  @Column(DataType.UUID)
  declare createdBy: string;

  @UpdatedAt
  @Column(DataType.DATE)
  declare updatedAt: Date;

  @Column(DataType.UUID)
  declare updatedBy: string;

  @Default(false)
  @Column(DataType.BOOLEAN)
  declare isDeleted: boolean;

  @Column(DataType.UUID)
  declare deletedBy: string;

  // Associations
  @BelongsTo(() => Question)
  question: Question;

  @BelongsTo(() => Company)
  company: Company;
}