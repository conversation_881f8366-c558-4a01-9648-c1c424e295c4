import { Injectable, UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import * as bcrypt from 'bcrypt';
import { ProductOwner } from 'src/database/models/product-owner-model';
import { Role } from 'src/database/models/role.model';

@Injectable()
export class AuthService {
  constructor(private jwtService: JwtService) { }

  async validateProductOwner(email: string, pass: string): Promise<any> {
    const user = await ProductOwner.findOne({ where: { email } });

    if (!user || !(await bcrypt.compare(pass, user.password))) {
      throw new UnauthorizedException('Invalid credentials');
    }
    
    return user;
  }
  
  async loginProductOwner(user: ProductOwner) {
    const role: any = await Role.findByPk(user.roleId);

    console.log('User role:', role?.dataValues?.name); 
    if (!role) {
      throw new UnauthorizedException('Role not found');
    }
    const payload = {
      email: user.email,
      sub: user.id,
      role: role?.dataValues?.name,
    };

    const accessToken = this.jwtService.sign(payload, {
      expiresIn: '15m',
    });

    const refreshToken = this.jwtService.sign(payload, {
      expiresIn: '7d',
    });

    const hashedRefreshToken = await bcrypt.hash(refreshToken, 10);
    await user.update({ refreshToken: hashedRefreshToken });

    return {
      access_token: accessToken,
      refresh_token: refreshToken,
      user: user
    };
  }

  async refreshProductOwnerTokens(userId: string, refreshToken: string) {
    const user = await ProductOwner.findByPk(userId);

    if (!user || !user.refreshToken) {
      throw new UnauthorizedException('Access Denied');
    }

    const isMatch = await bcrypt.compare(refreshToken, user.refreshToken);
    if (!isMatch) {
      throw new UnauthorizedException('Invalid refresh token');
    }

    const role: any = await Role.findByPk(user.roleId);
    if (!role) {
      throw new UnauthorizedException('Role not found');
    }

    const payload = { 
      email: user.email, 
      sub: user.id, 
      role: role?.dataValues?.name 
    };
    
    const newAccessToken = this.jwtService.sign(payload, { expiresIn: '15m' });
    const newRefreshToken = this.jwtService.sign(payload, { expiresIn: '7d' });

    const hashedNewRefreshToken = await bcrypt.hash(newRefreshToken, 10);
    await user.update({ refreshToken: hashedNewRefreshToken });

    return {
      access_token: newAccessToken,
      refresh_token: newRefreshToken,
    };
  }
}
