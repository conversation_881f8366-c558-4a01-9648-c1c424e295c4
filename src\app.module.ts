import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { sequelizeConfig } from './config/sequelize.config';
import { AuthModule } from './auth/auth.module';
import { ProductOwnerModule } from './product-owner/product-owner.module';
import { ReportsModule } from './reports/reports.module';
import { CategoriesModule } from './categories/categories.module';
import { SurveyModule } from './survey/survey.module';
import { DepartmentsModule } from './departments/departments.module';
// Import your models
import { User } from './database/models/user.model';
import { Role } from './database/models/role.model';
import { Company } from './database/models/company.model';
import { ProductOwner } from './database/models/product-owner-model';
import { Question } from './database/models/question.model';
import { QuestionOption } from './database/models/question-option.model';
import { CompanyQuestionMap } from './database/models/company-question-map.model';
import { AnswerCategory } from './database/models/answer-category.model';
import { SurveyResponse } from './database/models/survey-response.model';
import { Report } from './database/models/report.model';
import { Department } from './database/models/department.model';

@Module({
  imports: [
    SequelizeModule.forRoot({
      ...sequelizeConfig,
      models: [
        User,
        Role,
        Company,
        ProductOwner,
        Question,
        QuestionOption,
        CompanyQuestionMap,
        AnswerCategory,
        SurveyResponse,
        Report,
        Department
      ],
    }),
    AuthModule,
    ProductOwnerModule,
    ReportsModule,
    CategoriesModule,
    SurveyModule,
    DepartmentsModule,
    // Other modules
  ],
})
export class AppModule {}
