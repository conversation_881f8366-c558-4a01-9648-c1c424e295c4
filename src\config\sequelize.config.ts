import { SequelizeModuleOptions } from '@nestjs/sequelize';

export const sequelizeConfig: SequelizeModuleOptions = {
  dialect: 'mssql',
  host: 'localhost',
  port: 1433,
  username: "pratik_1101",
  password: 'pratik#123',
  database: "test-db",
  dialectOptions: {
    options: {
      encrypt: true,
      trustServerCertificate: true,
    },
    instanceName: 'SQLEXPRESS',
  },
  autoLoadModels: true,
  synchronize: false,
};

