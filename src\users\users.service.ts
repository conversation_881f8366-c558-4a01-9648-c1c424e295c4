import { Injectable, NotFoundException, BadRequestException, ForbiddenException } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { CreationAttributes, Op } from 'sequelize';
import * as bcrypt from 'bcrypt';
import { User } from '../database/models/user.model';
import { Role } from '../database/models/role.model';
import { Company } from '../database/models/company.model';
import { Department } from '../database/models/department.model';
import { CreateUserDto, UpdateUserDto, UserResponseDto, UserListResponseDto } from './dto/user.dto';
import { EmailService } from '../common/services/email.service';

@Injectable()
export class UsersService {
  constructor(
    @InjectModel(User)
    private readonly userModel: typeof User,
    @InjectModel(Role)
    private readonly roleModel: typeof Role,
    @InjectModel(Company)
    private readonly companyModel: typeof Company,
    @InjectModel(Department)
    private readonly departmentModel: typeof Department,
    private readonly emailService: EmailService,
  ) {}

  /**
   * Create a new user (CompanyAdmin only)
   */
  async createUser(
    companyId: string,
    dto: CreateUserDto,
    createdByUserId: string,
  ): Promise<UserResponseDto> {
    // Verify company exists
    const company = await this.companyModel.findOne({
      where: { id: companyId, isDeleted: false },
    });

    if (!company) {
      throw new NotFoundException('Company not found');
    }

    // Check if email already exists
    const existingUser = await this.userModel.findOne({
      where: {
        email: dto.email,
        isDeleted: false,
      },
    });

    if (existingUser) {
      throw new BadRequestException(`User with email '${dto.email}' already exists`);
    }

    // Get role by name
    const role = await this.roleModel.findOne({
      where: { name: dto.role, isDeleted: false },
    });

    if (!role) {
      throw new BadRequestException(`Role '${dto.role}' not found`);
    }

    // Validate department if provided
    let department: Department | null = null;
    if (dto.departmentId) {
      department = await this.departmentModel.findOne({
        where: {
          id: dto.departmentId,
          company_id: companyId,
          isDeleted: false
        },
      });

      if (!department) {
        throw new BadRequestException('Department not found or does not belong to your company');
      }
    }

    // Validate supervisor if provided
    let supervisor: any = null;
    if (dto.supervisorId) {
      supervisor = await this.userModel.findOne({
        where: {
          id: dto.supervisorId,
          company_id: companyId,
          isDeleted: false
        },
        include: [{ model: Role }]
      });

      if (!supervisor) {
        throw new BadRequestException('Supervisor not found or does not belong to your company');
      }

      // Validate supervisor role
      if (!['Supervisor', 'CompanyManager', 'CompanyAdmin'].includes(supervisor.role.name)) {
        throw new BadRequestException('Selected supervisor must have Supervisor, CompanyManager, or CompanyAdmin role');
      }
    }

    // Generate temporary password
    const temporaryPassword = this.emailService.generateTemporaryPassword();
    const hashedPassword = await bcrypt.hash(temporaryPassword, 10);

    // Create user
    const user = await this.userModel.create({
      firstName: dto.firstName,
      lastName: dto.lastName,
      email: dto.email,
      password: hashedPassword,
      roleId: role.id,
      company_id: companyId,
      department_id: dto.departmentId || null,
      supervisor_id: dto.supervisorId || null,
      isTemporaryPassword: true,
      isActive: true,
      createdBy: createdByUserId,
    } as CreationAttributes<User>);

    // Send welcome email with temporary password
    try {
      await this.emailService.sendWelcomeEmail(
        user.email,
        user.firstName,
        user.lastName,
        temporaryPassword,
        company.name
      );
    } catch (error) {
      console.error('Failed to send welcome email:', error);
      // Don't fail user creation if email fails
    }

    return this.mapToResponseDto(user, role, department, supervisor);
  }

  /**
   * Get users by company (CompanyAdmin can see all, Supervisor can see their team)
   */
  async getUsersByCompany(
    companyId: string,
    currentUserRole: string,
    currentUserId: string,
    filters?: {
      role?: string;
      department?: string;
      isActive?: boolean;
      page?: number;
      limit?: number;
    }
  ): Promise<UserListResponseDto> {
    const whereClause: any = {
      company_id: companyId,
      isDeleted: false,
    };

    // Role-based filtering
    if (currentUserRole === 'Supervisor') {
      // Supervisors can only see their direct reports
      whereClause.supervisor_id = currentUserId;
    }

    // Apply additional filters
    if (filters?.isActive !== undefined) {
      whereClause.isActive = filters.isActive;
    }

    // Build include array for joins
    const includeArray = [
      {
        model: Role,
        ...(filters?.role && { where: { name: filters.role } })
      },
      {
        model: Department,
        required: false,
        ...(filters?.department && { where: { name: filters.department } })
      },
      {
        model: User,
        as: 'supervisor',
        required: false,
        include: [{ model: Role }]
      }
    ];

    // Pagination
    const page = filters?.page || 1;
    const limit = filters?.limit || 10;
    const offset = (page - 1) * limit;

    const { rows: users, count: total } = await this.userModel.findAndCountAll({
      where: whereClause,
      include: includeArray,
      limit,
      offset,
      order: [['createdAt', 'DESC']],
    });

    const simplifiedUsers = users.map(user => user.get({ plain: true }));

    const userResponses = simplifiedUsers.map((user: any) => {
      return this.mapToResponseDto(user, user.role, user.department, user.supervisor);
    });

    return {
      users: [],
      total,
      page,
      limit,
    };
  }

  /**
   * Get user by ID
   */
  async getUserById(
    userId: string,
    companyId: string,
    currentUserRole: string,
    currentUserId: string
  ): Promise<UserResponseDto> {
    const whereClause: any = {
      id: userId,
      company_id: companyId,
      isDeleted: false,
    };

    // Role-based access control
    if (currentUserRole === 'Supervisor') {
      // Supervisors can only see their direct reports or themselves
      whereClause[Op.or] = [
        { supervisor_id: currentUserId },
        { id: currentUserId }
      ];
    } else if (currentUserRole === 'Employee') {
      // Employees can only see themselves
      whereClause.id = currentUserId;
    }

    const user: any = await this.userModel.findOne({
      where: whereClause,
      include: [
        { model: Role },
        { model: Department, required: false }
      ],
    });

    if (!user) {
      throw new NotFoundException('User not found or access denied');
    }

    let supervisor: any = null;
    if (user.supervisor_id) {
      supervisor = await this.userModel.findByPk(user.supervisor_id, {
        include: [{ model: Role }]
      });
    }

    return this.mapToResponseDto(user, user.role, user.department, supervisor);
  }

  /**
   * Update user (CompanyAdmin only)
   */
  async updateUser(
    userId: string,
    companyId: string,
    dto: UpdateUserDto,
    updatedByUserId: string,
  ): Promise<UserResponseDto> {
    const user = await this.userModel.findOne({
      where: {
        id: userId,
        company_id: companyId,
        isDeleted: false
      },
      include: [{ model: Role }]
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Prepare update data
    const updateData: any = {
      updatedBy: updatedByUserId,
    };

    // Update basic fields
    if (dto.firstName) updateData.firstName = dto.firstName;
    if (dto.lastName) updateData.lastName = dto.lastName;
    if (dto.email) updateData.email = dto.email;
    if (dto.isActive !== undefined) updateData.isActive = dto.isActive;

    // Update role if provided
    if (dto.role) {
      const role = await this.roleModel.findOne({
        where: { name: dto.role, isDeleted: false },
      });

      if (!role) {
        throw new BadRequestException(`Role '${dto.role}' not found`);
      }

      updateData.roleId = role.id;
    }

    // Update department if provided
    if (dto.departmentId) {
      const department = await this.departmentModel.findOne({
        where: {
          id: dto.departmentId,
          company_id: companyId,
          isDeleted: false
        },
      });

      if (!department) {
        throw new BadRequestException('Department not found or does not belong to your company');
      }

      updateData.department_id = dto.departmentId;
    }

    // Update supervisor if provided
    if (dto.supervisorId) {
      const supervisor = await this.userModel.findOne({
        where: {
          id: dto.supervisorId,
          company_id: companyId,
          isDeleted: false
        },
        include: [{ model: Role }]
      });

      if (!supervisor) {
        throw new BadRequestException('Supervisor not found or does not belong to your company');
      }

      // Validate supervisor role
      if (!['Supervisor', 'CompanyManager', 'CompanyAdmin'].includes(supervisor.role.name)) {
        throw new BadRequestException('Selected supervisor must have Supervisor, CompanyManager, or CompanyAdmin role');
      }

      updateData.supervisor_id = dto.supervisorId;
    }

    // Update user
    await user.update(updateData);

    // Fetch updated user with relations
    const updatedUser: any = await this.userModel.findByPk(userId, {
      include: [
        { model: Role },
        { model: Department, required: false }
      ],
    });

    if (!updatedUser) {
      throw new NotFoundException('Updated user not found');
    }

    let supervisor: any = null;
    if (updatedUser.supervisor_id) {
      supervisor = await this.userModel.findByPk(updatedUser.supervisor_id, {
        include: [{ model: Role }]
      });
    }

    return this.mapToResponseDto(updatedUser, updatedUser.role, updatedUser.department, supervisor);
  }

  /**
   * Delete user (CompanyAdmin only) - Soft delete
   */
  async deleteUser(
    userId: string,
    companyId: string,
    deletedByUserId: string,
  ): Promise<{ message: string }> {
    const user = await this.userModel.findOne({
      where: {
        id: userId,
        company_id: companyId,
        isDeleted: false
      },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Soft delete
    await user.update({
      isDeleted: true,
      deletedBy: deletedByUserId,
      isActive: false,
    });

    return { message: 'User deleted successfully' };
  }

  /**
   * Map User model to response DTO with role and department names
   */
  private mapToResponseDto(
    user: any,
    role: any,
    department?: any,
    supervisor?: any
  ): UserResponseDto {
    // Handle case where role might be undefined or attached to user object
    const userRole = role || user.role;

    if (!userRole) {
      console.error(`User ${user.id} role debug:`, {
        user: {
          id: user.id,
          email: user.email,
          roleId: user.roleId,
          role: user.role
        },
        passedRole: role
      });
      throw new BadRequestException(`User ${user.id} does not have a role assigned`);
    }

    return {
      id: user.id,
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      role: {
        id: userRole.id,
        name: userRole.name,
      },
      department: (department && department.id) ? {
        id: department.id,
        name: department.name,
      } : undefined,
      supervisor: supervisor ? {
        id: supervisor.id,
        firstName: supervisor.firstName,
        lastName: supervisor.lastName,
        role: supervisor.role?.name || 'Unknown',
      } : undefined,
      isActive: user.isActive,
      isTemporaryPassword: user.isTemporaryPassword,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      createdBy: user.createdBy,
      updatedBy: user.updatedBy,
    };
  }
}
