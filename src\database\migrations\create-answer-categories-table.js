'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    console.log('🚀 Starting migration: Creating AnswerCategories table...');
    
    try {
      await queryInterface.createTable('AnswerCategories', {
        id: {
          type: Sequelize.UUID,
          defaultValue: Sequelize.UUIDV4,
          primaryKey: true,
          allowNull: false
        },
        name: {
          type: Sequelize.STRING(50),
          allowNull: false,
          unique: true,
          comment: 'Category name (VERY_POSITIVE, POSITIVE, etc.)'
        },
        display_name: {
          type: Sequelize.STRING(100),
          allowNull: false,
          comment: 'Human readable name for display'
        },
        color_code: {
          type: Sequelize.STRING(7),
          allowNull: false,
          comment: 'Hex color code for graph visualization'
        },
        sort_order: {
          type: Sequelize.INTEGER,
          allowNull: false,
          defaultValue: 0,
          comment: 'Order for consistent sorting'
        },
        description: {
          type: Sequelize.TEXT,
          allowNull: true,
          comment: 'Detailed description of the category'
        },
        isActive: {
          type: Sequelize.BOOLEAN,
          defaultValue: true
        },
        createdAt: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
        },
        createdBy: {
          type: Sequelize.UUID,
          allowNull: true
        },
        updatedAt: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
        },
        updatedBy: {
          type: Sequelize.UUID,
          allowNull: true
        },
        isDeleted: {
          type: Sequelize.BOOLEAN,
          defaultValue: false
        },
        deletedBy: {
          type: Sequelize.UUID,
          allowNull: true
        }
      });
      
      console.log('✅ AnswerCategories table created successfully');

      // Insert predefined categories
      console.log('🔧 Inserting predefined answer categories...');
      await queryInterface.bulkInsert('AnswerCategories', [
        {
          id: 'a1b2c3d4-e5f6-4789-a012-123456789abc',
          name: 'VERY_POSITIVE',
          display_name: 'Very Positive',
          color_code: '#28a745',
          sort_order: 1,
          description: 'Excellent, Outstanding, Strongly Agree responses',
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          id: 'b2c3d4e5-f6a7-4890-b123-234567890bcd',
          name: 'POSITIVE',
          display_name: 'Positive',
          color_code: '#17a2b8',
          sort_order: 2,
          description: 'Good, Agree, Satisfied responses',
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          id: 'c3d4e5f6-a7b8-4901-c234-345678901cde',
          name: 'NEUTRAL',
          display_name: 'Neutral',
          color_code: '#ffc107',
          sort_order: 3,
          description: 'Okay, Neither Agree nor Disagree, Average responses',
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          id: 'd4e5f6a7-b8c9-4012-d345-456789012def',
          name: 'NEGATIVE',
          display_name: 'Negative',
          color_code: '#fd7e14',
          sort_order: 4,
          description: 'Bad, Disagree, Unsatisfied responses',
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          id: 'e5f6a7b8-c9d0-4123-e456-567890123efa',
          name: 'VERY_NEGATIVE',
          display_name: 'Very Negative',
          color_code: '#dc3545',
          sort_order: 5,
          description: 'Terrible, Strongly Disagree, Very Unsatisfied responses',
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ]);
      
      console.log('✅ Predefined categories inserted successfully');
      console.log('🎉 Migration completed: AnswerCategories table is ready!');
    } catch (error) {
      console.error('❌ Error during AnswerCategories migration:', error);
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    console.log('🔄 Rolling back migration: Dropping AnswerCategories table...');
    await queryInterface.dropTable('AnswerCategories');
    console.log('✅ AnswerCategories table dropped successfully');
    console.log('🔙 Migration rollback completed');
  }
};
