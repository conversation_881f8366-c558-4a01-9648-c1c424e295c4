import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Question } from 'src/database/models/question.model';
import { QuestionOption } from 'src/database/models/question-option.model';
import { CompanyQuestionMap } from 'src/database/models/company-question-map.model';
import { Company } from 'src/database/models/company.model';
import { CreateQuestionDto } from './dto/create-question.dto';
import { UpdateQuestionDto } from './dto/update-question.dto';
import { CreateQuestionOptionDto } from './dto/create-question-option.dto';
import { UpdateQuestionOptionDto } from './dto/update-question-option.dto';
import { AssignCompanyQuestionDto } from './dto/assign-company-question.dto';
import { CreationAttributes, Transaction } from 'sequelize';
import { Sequelize } from 'sequelize-typescript';

@Injectable()
export class QuestionService {
  constructor(
    @InjectModel(Question)
    private readonly questionModel: typeof Question,
    @InjectModel(QuestionOption)
    private readonly questionOptionModel: typeof QuestionOption,
    @InjectModel(CompanyQuestionMap)
    private readonly companyQuestionMapModel: typeof CompanyQuestionMap,
    @InjectModel(Company)
    private readonly companyModel: typeof Company,
    private readonly sequelize: Sequelize,
  ) {}

  // Question CRUD operations
  async getAllQuestions() {
    return this.questionModel.findAll({
      where: { isDeleted: false },
      include: [
        {
          model: QuestionOption,
          where: { isDeleted: false },
          required: false,
        },
      ],
    });
  }

  async getQuestionById(id: string) {
    const question = await this.questionModel.findOne({
      where: { id, isDeleted: false },
      include: [
        {
          model: QuestionOption,
          where: { isDeleted: false },
          required: false,
        },
      ],
    });

    if (!question) {
      throw new NotFoundException(`Question with ID ${id} not found`);
    }

    return question;
  }

  async createQuestion(createQuestionDto: CreateQuestionDto) {
    console.log('createQuestionDto', createQuestionDto);
    const transaction = await this.sequelize.transaction();

    try {
      // Create the question
      const question = await this.questionModel.create(
        {
          question_text: createQuestionDto.question_text,
          createdBy: createQuestionDto.createdBy || '',
        } as CreationAttributes<Question>,
        { transaction },
      );

      // Create the options
      if (createQuestionDto.options && createQuestionDto.options.length > 0) {
        const optionsWithQuestionId = createQuestionDto.options.map(option => ({
          option_text: option.option_text,
          option_value: option.option_value,
          category_id: option.categoryId,
          question_id: question.id,
          createdBy: createQuestionDto.createdBy || '',
        }));

        await this.questionOptionModel.bulkCreate(
          optionsWithQuestionId as CreationAttributes<QuestionOption>[],
          { transaction }
        );
      }

      await transaction.commit();

      // Return the created question with its options
      return this.getQuestionById(question.id);
    } catch (error) {
      await transaction.rollback();
      throw new BadRequestException(`Failed to create question: ${error.message}`);
    }
  }

  async updateQuestion(id: string, updateQuestionDto: UpdateQuestionDto) {
    const question = await this.getQuestionById(id);

    await question.update({
      ...updateQuestionDto,
    });

    return this.getQuestionById(id);
  }

  async deleteQuestion(id: string, userId: string) {
    const question = await this.getQuestionById(id);

    await question.update({
      isDeleted: true,
      deletedBy: userId,
    });

    return { message: 'Question deleted successfully' };
  }

  // Question Option operations
  async addQuestionOption(questionId: string, createOptionDto: CreateQuestionOptionDto) {
    // Verify question exists
    await this.getQuestionById(questionId);

    const option = await this.questionOptionModel.create({
      option_text: createOptionDto.option_text,
      option_value: createOptionDto.option_value,
      category_id: createOptionDto.categoryId,
      question_id: questionId,
      createdBy: createOptionDto.createdBy,
    } as CreationAttributes<QuestionOption>);

    return option;
  }

  async updateQuestionOption(questionId: string, optionId: string, updateOptionDto: UpdateQuestionOptionDto) {
    const option = await this.questionOptionModel.findOne({
      where: {
        id: optionId,
        question_id: questionId,
        isDeleted: false,
      },
    });

    if (!option) {
      throw new NotFoundException(`Option with ID ${optionId} not found for question ${questionId}`);
    }

    await option.update({
      ...updateOptionDto,
    });

    return option;
  }

  async deleteQuestionOption(questionId: string, optionId: string, userId: string) {
    const option = await this.questionOptionModel.findOne({
      where: {
        id: optionId,
        question_id: questionId,
        isDeleted: false,
      },
    });

    if (!option) {
      throw new NotFoundException(`Option with ID ${optionId} not found for question ${questionId}`);
    }

    await option.update({
      isDeleted: true,
      deletedBy: userId,
    });

    return { message: 'Option deleted successfully' };
  }

  // Company Question Mapping operations
  async getCompanyQuestions(companyId: string) {
    // Verify company exists
    const company = await this.companyModel.findOne({
      where: { id: companyId, isDeleted: false },
    });

    if (!company) {
      throw new NotFoundException(`Company with ID ${companyId} not found`);
    }

    // Get all questions mapped to this company
    const mappings = await this.companyQuestionMapModel.findAll({
      where: {
        company_id: companyId,
        isDeleted: false,
      },
      include: [
        {
          model: Question,
          include: [
            {
              model: QuestionOption,
              where: { isDeleted: false },
              required: false,
            },
          ],
        },
      ],
    });

    return mappings.map(mapping => mapping.question);
  }

  async assignQuestionsToCompany(companyId: string, assignDto: AssignCompanyQuestionDto) {
    // Verify company exists
    const company = await this.companyModel.findOne({
      where: { id: companyId, isDeleted: false },
    });

    if (!company) {
      throw new NotFoundException(`Company with ID ${companyId} not found`);
    }

    const transaction = await this.sequelize.transaction();

    try {
      // Verify all questions exist
      for (const questionId of assignDto.questionIds) {
        await this.getQuestionById(questionId);
      }

      // Create mappings for each question
      const mappings: CompanyQuestionMap[] = [];
      for (const questionId of assignDto.questionIds) {
        // Check if mapping already exists
        const existingMapping = await this.companyQuestionMapModel.findOne({
          where: {
            company_id: companyId,
            question_id: questionId,
            isDeleted: false,
          },
          transaction,
        });

        if (!existingMapping) {
          const mapping = await this.companyQuestionMapModel.create(
            {
              company_id: companyId,
              question_id: questionId,
              createdBy: assignDto.createdBy || '',
            } as CreationAttributes<CompanyQuestionMap>,
            { transaction },
          );
          mappings.push(mapping);
        }
      }

      await transaction.commit();
      return { message: `${mappings.length} questions assigned to company successfully` };
    } catch (error) {
      await transaction.rollback();
      throw new BadRequestException(`Failed to assign questions: ${error.message}`);
    }
  }

  async removeQuestionFromCompany(companyId: string, questionId: string, userId: string) {
    const mapping = await this.companyQuestionMapModel.findOne({
      where: {
        company_id: companyId,
        question_id: questionId,
        isDeleted: false,
      },
    });

    if (!mapping) {
      throw new NotFoundException(`Question ${questionId} is not assigned to company ${companyId}`);
    }

    await mapping.update({
      isDeleted: true,
      deletedBy: userId,
    });

    return { message: 'Question removed from company successfully' };
  }
}




