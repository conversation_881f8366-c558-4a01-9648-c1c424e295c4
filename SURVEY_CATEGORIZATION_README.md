# Survey Answer Categorization System

This document explains the new survey answer categorization system that enables unified reporting and graph generation for supervisors and company admins.

## 🎯 Overview

The system categorizes all survey answers into 5 sentiment categories:
- **Very Positive** (Green #28a745) - Excellent, Outstanding, Strongly Agree
- **Positive** (Blue #17a2b8) - Good, Agree, Satisfied  
- **Neutral** (Yellow #ffc107) - Okay, Neither Agree nor Disagree, Average
- **Negative** (Orange #fd7e14) - Bad, Disagree, Unsatisfied
- **Very Negative** (Red #dc3545) - Terrible, Strongly Disagree, Very Unsatisfied

## 🗄️ Database Changes

### New Tables Created:
1. **AnswerCategories** - Stores the 5 predefined sentiment categories
2. **SurveyResponses** - Stores individual survey responses with category mapping

### Modified Tables:
1. **QuestionOptions** - Added `category_id` foreign key to link options to categories

## 🚀 Setup Instructions

### 1. Run Database Migrations (In Order)

```bash
# Step 1: Create AnswerCategories table with predefined categories
npx sequelize-cli db:migrate --name create-answer-categories-table.js

# Step 2: Add category_id to QuestionOptions table  
npx sequelize-cli db:migrate --name add-category-id-to-question-options.js

# Step 3: Create SurveyResponses table
npx sequelize-cli db:migrate --name create-survey-responses-table.js
```

### 2. Run Seeders (Optional)

```bash
# Populate answer categories (if not done in migration)
npx sequelize-cli db:seed --seed answer-categories-seeder.js
```

### 3. Verify Setup

```bash
# Check migration status
npx sequelize-cli db:migrate:status

# Verify tables exist
# Check your database for: AnswerCategories, SurveyResponses, and updated QuestionOptions
```

## 📊 API Endpoints

### Reports API

#### Company Analytics
```http
GET /api/reports/company/{companyId}/survey-analytics
Query Parameters:
- startDate (optional): YYYY-MM-DD
- endDate (optional): YYYY-MM-DD  
- departmentFilter (optional): string
```

#### Supervisor Team Analytics
```http
GET /api/reports/supervisor/{supervisorId}/team-survey
Query Parameters:
- startDate (optional): YYYY-MM-DD
- endDate (optional): YYYY-MM-DD
```

#### Trend Analysis
```http
GET /api/reports/company/{companyId}/trend-analysis
Query Parameters:
- startDate (optional): YYYY-MM-DD
- endDate (optional): YYYY-MM-DD
- interval (optional): daily|weekly|monthly
```

#### Department Analytics
```http
GET /api/reports/company/{companyId}/department-analytics
Query Parameters:
- startDate (optional): YYYY-MM-DD
- endDate (optional): YYYY-MM-DD
```

### Categories API

#### Get All Categories
```http
GET /api/categories
```

#### Get Category Usage Stats
```http
GET /api/categories/usage-stats
```

### Survey Submission API

#### Submit Survey
```http
POST /api/survey/submit
Body: {
  "userId": "uuid",
  "companyId": "uuid", 
  "responses": [
    {
      "questionId": "uuid",
      "selectedOptionId": "uuid"
    }
  ],
  "department": "Engineering"
}
```

#### Get User Survey History
```http
GET /api/survey/user/{userId}/history?limit=10
```

## 📈 Sample API Response

### Company Survey Analytics Response:
```json
{
  "companyId": "123e4567-e89b-12d3-a456-************",
  "companyName": "Tech Solutions Inc",
  "reportPeriod": {
    "startDate": "2024-01-01T00:00:00.000Z",
    "endDate": "2024-01-31T23:59:59.999Z"
  },
  "totalResponses": 450,
  "totalEmployees": 120,
  "responseRate": 75.5,
  "categoryBreakdown": [
    {
      "categoryId": "11111111-1111-1111-1111-111111111111",
      "categoryName": "VERY_POSITIVE",
      "displayName": "Very Positive",
      "colorCode": "#28a745",
      "responseCount": 135,
      "percentage": 30.0,
      "trend": "+5.2"
    }
    // ... other categories
  ],
  "timeSeriesData": [
    {
      "date": "2024-01-01",
      "VERY_POSITIVE": 25,
      "POSITIVE": 30,
      "NEUTRAL": 15,
      "NEGATIVE": 8,
      "VERY_NEGATIVE": 2
    }
    // ... more time points
  ],
  "departmentBreakdown": [
    {
      "departmentName": "Engineering",
      "totalResponses": 120,
      "categories": {
        "VERY_POSITIVE": 40,
        "POSITIVE": 45,
        "NEUTRAL": 25,
        "NEGATIVE": 8,
        "VERY_NEGATIVE": 2
      }
    }
    // ... other departments
  ]
}
```

## 🔧 How It Works

1. **Question Options Categorization**: Each question option is automatically mapped to one of the 5 sentiment categories based on text patterns.

2. **Survey Response Storage**: When users submit surveys, responses are stored in `SurveyResponses` table with links to the categorized options.

3. **Unified Reporting**: All reports aggregate data by sentiment categories, enabling consistent visualization across different questions.

4. **Automatic Mapping**: The system includes intelligent text pattern matching to automatically categorize existing question options.

## 🎨 Frontend Integration

Use the provided mock data structure and API endpoints to build:
- Pie charts for sentiment distribution
- Line charts for trend analysis  
- Bar charts for department comparisons
- Data tables for detailed breakdowns

## 🔍 Troubleshooting

### Common Issues:

1. **Migration Order**: Ensure migrations run in the correct order (categories → options → responses)

2. **Foreign Key Constraints**: Make sure AnswerCategories table exists before running the QuestionOptions migration

3. **Existing Data**: The system automatically maps existing question options to categories, but you may need to manually adjust some mappings

### Rollback Instructions:

```bash
# Rollback migrations in reverse order
npx sequelize-cli db:migrate:undo --name create-survey-responses-table.js
npx sequelize-cli db:migrate:undo --name add-category-id-to-question-options.js  
npx sequelize-cli db:migrate:undo --name create-answer-categories-table.js
```

## 📝 Notes

- The system preserves all existing functionality while adding categorization
- Categories are predefined but can be customized through the Categories API
- All survey data is stored with full audit trails (created/updated/deleted tracking)
- The system supports both company-wide and supervisor-specific reporting

## 🚀 Next Steps

1. Run the migrations in your development environment
2. Test the API endpoints with sample data
3. Integrate the frontend components using the provided mock data structure
4. Deploy to staging for user acceptance testing

For questions or issues, refer to the API documentation or contact the development team.
