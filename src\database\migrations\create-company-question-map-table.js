'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    console.log('🚀 Starting migration: Creating CompanyQuestionMaps table...');

    await queryInterface.createTable('CompanyQuestionMaps', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.literal('(NEWID())'),
        primaryKey: true,
        allowNull: false
      },
      company_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'Companies',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      question_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'Questions',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      isActive: {
        type: Sequelize.BOOLEAN,
        defaultValue: true
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      },
      createdBy: {
        type: Sequelize.UUID,
        allowNull: true
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      },
      updatedBy: {
        type: Sequelize.UUID,
        allowNull: true
      },
      isDeleted: {
        type: Sequelize.BOOLEAN,
        defaultValue: false
      },
      deletedBy: {
        type: Sequelize.UUID,
        allowNull: true
      }
    });
    console.log('✅ CompanyQuestionMaps table created successfully');

    // Add a unique constraint to prevent duplicate mappings
    console.log('🔧 Adding unique constraint for company_id and question_id...');
    await queryInterface.addConstraint('CompanyQuestionMaps', {
      fields: ['company_id', 'question_id'],
      type: 'unique',
      name: 'unique_company_question'
    });
    console.log('✅ Unique constraint added successfully');
    console.log('🎉 Migration completed: CompanyQuestionMaps table is ready!');
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('CompanyQuestionMaps');
  }
};