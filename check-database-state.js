const { Sequelize } = require('sequelize');

// Database configuration (adjust as needed)
const sequelize = new Sequelize({
  dialect: 'mssql',
  host: 'localhost',
  port: 1433,
  username: "pratik_1101",
  password: 'pratik#123',
  database: "test-db",
  dialectOptions: {
    options: {
      encrypt: true,
      trustServerCertificate: true,
    },
    instanceName: 'SQLEXPRESS',
  },
  logging: console.log
});

async function checkDatabaseState() {
  try {
    console.log('🔍 Checking database state...');
    
    // Check if AnswerCategories table exists
    const answerCategoriesExists = await sequelize.query(`
      SELECT COUNT(*) as count 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_NAME = 'AnswerCategories'
    `, { type: Sequelize.QueryTypes.SELECT });
    
    console.log(`AnswerCategories table exists: ${answerCategoriesExists[0].count > 0}`);
    
    if (answerCategoriesExists[0].count > 0) {
      const categoriesCount = await sequelize.query(`
        SELECT COUNT(*) as count FROM AnswerCategories WHERE isDeleted = 0
      `, { type: Sequelize.QueryTypes.SELECT });
      console.log(`Answer categories count: ${categoriesCount[0].count}`);
    }
    
    // Check if QuestionOptions table exists and has category_id column
    const questionOptionsExists = await sequelize.query(`
      SELECT COUNT(*) as count 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_NAME = 'QuestionOptions'
    `, { type: Sequelize.QueryTypes.SELECT });
    
    console.log(`QuestionOptions table exists: ${questionOptionsExists[0].count > 0}`);
    
    if (questionOptionsExists[0].count > 0) {
      const categoryIdExists = await sequelize.query(`
        SELECT COUNT(*) as count 
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = 'QuestionOptions' AND COLUMN_NAME = 'category_id'
      `, { type: Sequelize.QueryTypes.SELECT });
      
      console.log(`QuestionOptions.category_id column exists: ${categoryIdExists[0].count > 0}`);
      
      if (categoryIdExists[0].count > 0) {
        const optionsWithCategories = await sequelize.query(`
          SELECT COUNT(*) as count FROM QuestionOptions WHERE category_id IS NOT NULL
        `, { type: Sequelize.QueryTypes.SELECT });
        
        const totalOptions = await sequelize.query(`
          SELECT COUNT(*) as count FROM QuestionOptions WHERE isDeleted = 0
        `, { type: Sequelize.QueryTypes.SELECT });
        
        console.log(`Question options with categories: ${optionsWithCategories[0].count}/${totalOptions[0].count}`);
      }
    }
    
    // Check if SurveyResponses table exists
    const surveyResponsesExists = await sequelize.query(`
      SELECT COUNT(*) as count 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_NAME = 'SurveyResponses'
    `, { type: Sequelize.QueryTypes.SELECT });
    
    console.log(`SurveyResponses table exists: ${surveyResponsesExists[0].count > 0}`);
    
    console.log('✅ Database state check completed');
    
  } catch (error) {
    console.error('❌ Error checking database state:', error.message);
  } finally {
    await sequelize.close();
  }
}

checkDatabaseState();
