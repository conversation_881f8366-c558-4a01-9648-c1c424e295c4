import { Injectable, UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import * as bcrypt from 'bcrypt';
import { User } from 'src/database/models/user.model';
import { Role } from 'src/database/models/role.model';

@Injectable()
export class UserAuthService {
  constructor(private jwtService: JwtService) { }

  async validateUser(email: string, pass: string): Promise<any> {
    const user = await User.findOne({ 
      where: { 
        email,
        isActive: true,
        isDeleted: false
      } 
    });

    console.log("user", user);
    console.log("pass", pass)

    if (!user?.dataValues) {
      throw new UnauthorizedException('Invalid credentials');
    }

    // Check if password exists before comparing
    if (!user?.dataValues?.password || !pass) {
      throw new UnauthorizedException('Invalid credentials');
    }

    const isPasswordValid = await bcrypt.compare(pass, user?.dataValues.password);
    if (!isPasswordValid) {
      throw new UnauthorizedException('Invalid credentials');
    }
    
    // Check if user is CompanyAdmin or Supervisor
    const role = await Role.findByPk(user?.dataValues?.roleId);
    console.log("role===>", role)
    if (!role || !['CompanyAdmin', 'Supervisor'].includes(role?.dataValues?.name)) {
      throw new UnauthorizedException('Unauthorized role');
    }
    
    return user;
  }
  
  async login(user: User) {
    const role: any = await Role.findByPk(user?.dataValues?.roleId);

    console.log("user=====================>", user)
    console.log("rolllllll===============>", role)

    if (!role.dataValues) {
      throw new UnauthorizedException('Role not found');
    }
    
    const payload = {
      email: user.email,
      sub: user.id,
      role: role.dataValues.name,
      companyId: user.company_id
    };

    const accessToken = this.jwtService.sign(payload, {
      expiresIn: '15m',
    });

    const refreshToken = this.jwtService.sign(payload, {
      expiresIn: '7d',
    });

    const hashedRefreshToken = await bcrypt.hash(refreshToken, 10);
    await user.update({ refreshToken: hashedRefreshToken });

    return {
      access_token: accessToken,
      refresh_token: refreshToken,
      user: {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        role: role.dataValues.name,
        companyId: user.company_id
      }
    };
  }

  async refreshTokens(userId: string, refreshToken: string) {
    const user = await User.findByPk(userId);

    if (!user || !user.refreshToken) {
      throw new UnauthorizedException('Access Denied');
    }

    const isMatch = await bcrypt.compare(refreshToken, user.refreshToken);
    if (!isMatch) {
      throw new UnauthorizedException('Invalid refresh token');
    }

    const role: any = await Role.findByPk(user.roleId);
    if (!role) {
      throw new UnauthorizedException('Role not found');
    }

    const payload = { 
      email: user.email, 
      sub: user.id, 
      role: role.dataValues.name,
      companyId: user.company_id
    };
    
    const newAccessToken = this.jwtService.sign(payload, { expiresIn: '15m' });
    const newRefreshToken = this.jwtService.sign(payload, { expiresIn: '7d' });

    const hashedNewRefreshToken = await bcrypt.hash(newRefreshToken, 10);
    await user.update({ refreshToken: hashedNewRefreshToken });

    return {
      access_token: newAccessToken,
      refresh_token: newRefreshToken,
    };
  }
}


