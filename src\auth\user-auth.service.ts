import { Injectable, UnauthorizedException, BadRequestException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import * as bcrypt from 'bcrypt';
import { User } from 'src/database/models/user.model';
import { Role } from 'src/database/models/role.model';
import { Department } from 'src/database/models/department.model';
import { ChangePasswordDto } from 'src/users/dto/user.dto';

@Injectable()
export class UserAuthService {
  constructor(private jwtService: JwtService) { }

  async validateUser(email: string, pass: string): Promise<any> {
    const user = await User.findOne({ 
      where: { 
        email,
        isActive: true,
        isDeleted: false
      } 
    });

    console.log("user", user);
    console.log("pass", pass)

    if (!user?.dataValues) {
      throw new UnauthorizedException('Invalid credentials');
    }
    console.log("user11111====>", user.dataValues);


    // Check if password exists before comparing
    if (!user?.dataValues?.password || !pass) {
      throw new UnauthorizedException('Invalid credentials');
    }
    console.log("user2222====>", user.dataValues.password );


    const isPasswordValid = await bcrypt.compare(pass, user?.dataValues.password);

    console.log("isPasswordValid ===>", isPasswordValid);
    if (!isPasswordValid) {
      throw new UnauthorizedException('Invalid credentials');
    }
    
    // Check if user is CompanyAdmin or Supervisor
    const role = await Role.findByPk(user?.dataValues?.roleId);
    console.log("role===>", role)
    if (!role || !['CompanyAdmin', 'Supervisor'].includes(role?.dataValues?.name)) {
      throw new UnauthorizedException('Unauthorized role');
    }
    
    return user;
  }
  
  async login(user: User) {
    const role: any = await Role.findByPk(user?.dataValues?.roleId);

    console.log("user=====================>", user)
    console.log("rolllllll===============>", role)

    if (!role.dataValues) {
      throw new UnauthorizedException('Role not found');
    }

    // Get user with department and supervisor info for complete response
    // Note: CompanyAdmin users don't have departments, so we use required: false
    let fullUser: any = null;
    let supervisor: any = null;

    try {
      fullUser = await User.findByPk(user.id, {
        include: [
          { model: Role },
          { model: Department, required: false } // This allows null departments for CompanyAdmin
        ]
      });

      if (fullUser?.supervisor_id) {
        supervisor = await User.findByPk(fullUser.supervisor_id, {
          include: [{ model: Role }]
        });
      }
    } catch (error) {
      console.log('Error fetching user details:', error);
      // Fallback: use basic user info if associations fail
      fullUser = user;
    }

    const payload = {
      email: user.email,
      sub: user.id,
      role: role.dataValues.name,
      companyId: user?.dataValues?.company_id
    };

    const accessToken = this.jwtService.sign(payload, {
      expiresIn: '15m',
    });

    const refreshToken = this.jwtService.sign(payload, {
      expiresIn: '7d',
    });

    const hashedRefreshToken = await bcrypt.hash(refreshToken, 10);
    await user.update({ refreshToken: hashedRefreshToken });

    const response = {
      access_token: accessToken,
      refresh_token: refreshToken,
      user: {
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        role: {
          id: role.id,
          name: role.dataValues.name,
        },
        department: (fullUser?.department && fullUser.department.id) ? {
          id: fullUser.department.id,
          name: fullUser.department.name,
        } : null,
        supervisor: supervisor ? {
          id: supervisor.id,
          firstName: supervisor.firstName,
          lastName: supervisor.lastName,
          role: supervisor.role?.name || 'Unknown',
        } : undefined,
        companyId: user.company_id
      },
      requiresPasswordChange: user.isTemporaryPassword || false
    };

    // Add message if password change is required
    if (user.isTemporaryPassword) {
      response['message'] = 'You must change your password before proceeding';
    }

    return response;
  }

  async refreshTokens(userId: string, refreshToken: string) {
    const user = await User.findByPk(userId);

    if (!user || !user.refreshToken) {
      throw new UnauthorizedException('Access Denied');
    }

    const isMatch = await bcrypt.compare(refreshToken, user.refreshToken);
    if (!isMatch) {
      throw new UnauthorizedException('Invalid refresh token');
    }

    const role: any = await Role.findByPk(user.roleId);
    if (!role) {
      throw new UnauthorizedException('Role not found');
    }

    const payload = { 
      email: user.email, 
      sub: user.id, 
      role: role.dataValues.name,
      companyId: user.company_id
    };
    
    const newAccessToken = this.jwtService.sign(payload, { expiresIn: '15m' });
    const newRefreshToken = this.jwtService.sign(payload, { expiresIn: '7d' });

    const hashedNewRefreshToken = await bcrypt.hash(newRefreshToken, 10);
    await user.update({ refreshToken: hashedNewRefreshToken });

    return {
      access_token: newAccessToken,
      refresh_token: newRefreshToken,
    };
  }

  /**
   * Change password for users with temporary passwords
   */
  async forceChangePassword(
    userId: string,
    currentPassword: string,
    newPassword: string,
    confirmPassword: string
  ) {
    // Validate new password matches confirmation
    if (newPassword !== confirmPassword) {
      throw new BadRequestException('New password and confirmation do not match');
    }

    // Get user
    const user = await User.findByPk(userId);
    if (!user) {
      throw new UnauthorizedException('User not found');
    }

    // Verify current password
    const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password);
    if (!isCurrentPasswordValid) {
      throw new UnauthorizedException('Current password is incorrect');
    }

    // Ensure user has temporary password
    if (!user.isTemporaryPassword) {
      throw new BadRequestException('This endpoint is only for users with temporary passwords');
    }

    // Hash new password
    const hashedNewPassword = await bcrypt.hash(newPassword, 10);

    // Update user password and remove temporary flag
    await user.update({
      password: hashedNewPassword,
      isTemporaryPassword: false,
    });

    // Generate new tokens
    const role: any = await Role.findByPk(user.roleId);
    if (!role) {
      throw new UnauthorizedException('Role not found');
    }

    const payload = {
      email: user.email,
      sub: user.id,
      role: role.dataValues.name,
      companyId: user.company_id
    };

    const accessToken = this.jwtService.sign(payload, {
      expiresIn: '15m',
    });

    const refreshToken = this.jwtService.sign(payload, {
      expiresIn: '7d',
    });

    const hashedRefreshToken = await bcrypt.hash(refreshToken, 10);
    await user.update({ refreshToken: hashedRefreshToken });

    return {
      message: 'Password changed successfully',
      access_token: accessToken,
      refresh_token: refreshToken,
    };
  }

  /**
   * Regular password change for authenticated users
   */
  async changePassword(
    userId: string,
    currentPassword: string,
    newPassword: string,
    confirmPassword: string
  ) {
    // Validate new password matches confirmation
    if (newPassword !== confirmPassword) {
      throw new BadRequestException('New password and confirmation do not match');
    }

    // Get user
    const user = await User.findByPk(userId);
    if (!user) {
      throw new UnauthorizedException('User not found');
    }

    // Verify current password
    const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password);
    if (!isCurrentPasswordValid) {
      throw new UnauthorizedException('Current password is incorrect');
    }

    // Hash new password
    const hashedNewPassword = await bcrypt.hash(newPassword, 10);

    // Update user password
    await user.update({
      password: hashedNewPassword,
      isTemporaryPassword: false, // Ensure temporary flag is removed
    });

    return {
      message: 'Password changed successfully',
    };
  }
}


