'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('Companies', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.literal('(NEWID())'),
        primaryKey: true,
        allowNull: false
      },
      name: {
        type: Sequelize.STRING(150),
        allowNull: false
      },
      product_owner_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'ProductOwners',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'NO ACTION'
      },
    //   company_admin_user_id: {
    //     type: Sequelize.UUID,
    //     allowNull: false,
    //     references: {
    //       model: 'Users',
    //       key: 'id'
    //     },
    //     onUpdate: 'CASCADE',
    //     onDelete: 'NO ACTION'
    //   },
      contact_person_firstName: {
        type: Sequelize.STRING(150),
        allowNull: false
      },
      contact_person_lastName: {
        type: Sequelize.STRING(150),
        allowNull: false
      },
      contact_person_email: {
        type: Sequelize.STRING(150),
        allowNull: false
      },
      contact_person_phone: {
        type: Sequelize.STRING(20),
        allowNull: false
      },
      address_line: {
        type: Sequelize.STRING(255),
        allowNull: false
      },
      zipcode: {
        type: Sequelize.STRING(20),
        allowNull: false
      },
      country: {
        type: Sequelize.STRING(100),
        allowNull: false
      },
      state: {
        type: Sequelize.STRING(100),
        allowNull: false
      },
      city: {
        type: Sequelize.STRING(100),
        allowNull: false
      },
      isActive: {
        type: Sequelize.BOOLEAN,
        defaultValue: true
      },
      createdAt: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('GETDATE()')
      },
      createdBy: {
        type: Sequelize.UUID,
        allowNull: true
      },
      updatedAt: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('GETDATE()')
      },
      updatedBy: {
        type: Sequelize.UUID,
        allowNull: true
      },
      isDeleted: {
        type: Sequelize.BOOLEAN,
        defaultValue: false
      },
      deletedBy: {
        type: Sequelize.UUID,
        allowNull: true
      }
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('Companies');
  }
};
