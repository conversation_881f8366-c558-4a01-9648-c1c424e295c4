'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('Users', 'department_id', {
      type: Sequelize.UUID,
      allowNull: true,
      references: {
        model: 'Departments',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL'
    });

    // Add index for performance
    await queryInterface.addIndex('Users', ['department_id'], {
      name: 'idx_users_department_id'
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeIndex('Users', 'idx_users_department_id');
    await queryInterface.removeColumn('Users', 'department_id');
  }
};
