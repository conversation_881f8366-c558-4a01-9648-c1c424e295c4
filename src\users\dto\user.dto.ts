import { IsString, <PERSON>NotEmpty, <PERSON><PERSON>ptional, IsUUID, IsBoolean, MaxLength, IsEmail, IsIn } from 'class-validator';

export class CreateUserDto {
  @IsString()
  @IsNotEmpty()
  @MaxLength(150)
  firstName: string;

  @IsString()
  @IsNotEmpty()
  @MaxLength(150)
  lastName: string;

  @IsEmail()
  @IsNotEmpty()
  @MaxLength(150)
  email: string;

  @IsString()
  @IsNotEmpty()
  @IsIn(['Employee', 'CompanyAdmin', 'CompanyManager', 'Supervisor'])
  role: string;

  @IsOptional()
  @IsUUID('4')
  departmentId?: string;

  @IsOptional()
  @IsUUID('4')
  supervisorId?: string;

  @IsOptional()
  @IsUUID('4')
  createdBy?: string;
}

export class UpdateUserDto {
  @IsString()
  @IsOptional()
  @MaxLength(150)
  firstName?: string;

  @IsString()
  @IsOptional()
  @MaxLength(150)
  lastName?: string;

  @IsEmail()
  @IsOptional()
  @MaxLength(150)
  email?: string;

  @IsString()
  @IsOptional()
  @IsIn(['Employee', 'CompanyAdmin', 'CompanyManager', 'Supervisor'])
  role?: string;

  @IsOptional()
  @IsUUID('4')
  departmentId?: string;

  @IsOptional()
  @IsUUID('4')
  supervisorId?: string;

  @IsBoolean()
  @IsOptional()
  isActive?: boolean;

  @IsOptional()
  @IsUUID('4')
  updatedBy?: string;
}

export class UserResponseDto {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  role: {
    id: string;
    name: string;
  };
  department?: {
    id: string;
    name: string;
  };
  supervisor?: {
    id: string;
    firstName: string;
    lastName: string;
    role: string;
  };
  isActive: boolean;
  isTemporaryPassword: boolean;
  createdAt: Date;
  updatedAt?: Date;
  createdBy?: string;
  updatedBy?: string;
}

export class ChangePasswordDto {
  @IsString()
  @IsNotEmpty()
  currentPassword: string;

  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  newPassword: string;

  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  confirmPassword: string;
}

export class UserListResponseDto {
  users: UserResponseDto[];
  total: number;
  page?: number;
  limit?: number;
}
