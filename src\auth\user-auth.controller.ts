import { Controller, Post, Body, UnauthorizedException } from '@nestjs/common';
import { UserAuthService } from './user-auth.service';

@Controller('user-auth')
export class UserAuthController {
  constructor(private readonly userAuthService: UserAuthService) {}

  @Post('login')
  async login(@Body() body: { email: string; password: string }) {
    const user = await this.userAuthService.validateUser(body.email, body.password);
    return this.userAuthService.login(user);
  }

  @Post('refresh-token')
  async refreshToken(@Body() body: { userId: string; refreshToken: string }) {
    return this.userAuthService.refreshTokens(body.userId, body.refreshToken);
  }
}