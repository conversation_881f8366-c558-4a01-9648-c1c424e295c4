import { Controller, Post, Body, UnauthorizedException, UseGuards, BadRequestException } from '@nestjs/common';
import { UserAuthService } from './user-auth.service';
import { JwtAuthGuard } from './guards/jwt-auth-guards';
import { CurrentUser } from './current-user-decorator';
import { ChangePasswordDto } from '../users/dto/user.dto';

@Controller('user-auth')
export class UserAuthController {
  constructor(private readonly userAuthService: UserAuthService) {}

  @Post('login')
  async login(@Body() body: { email: string; password: string }) {
    const user = await this.userAuthService.validateUser(body.email, body.password);
    return this.userAuthService.login(user);
  }

  @Post('refresh-token')
  async refreshToken(@Body() body: { userId: string; refreshToken: string }) {
    return this.userAuthService.refreshTokens(body.userId, body.refreshToken);
  }

  @Post('force-change-password')
  @UseGuards(JwtAuthGuard)
  async forceChangePassword(
    @Body() changePasswordDto: ChangePasswordDto,
    @CurrentUser() user: any,
  ) {
    try {
      return await this.userAuthService.forceChangePassword(
        user.sub,
        changePasswordDto.currentPassword,
        changePasswordDto.newPassword,
        changePasswordDto.confirmPassword,
      );
    } catch (error) {
      throw new BadRequestException(`Failed to change password: ${error.message}`);
    }
  }

  @Post('change-password')
  @UseGuards(JwtAuthGuard)
  async changePassword(
    @Body() changePasswordDto: ChangePasswordDto,
    @CurrentUser() user: any,
  ) {
    try {
      return await this.userAuthService.changePassword(
        user.sub,
        changePasswordDto.currentPassword,
        changePasswordDto.newPassword,
        changePasswordDto.confirmPassword,
      );
    } catch (error) {
      throw new BadRequestException(`Failed to change password: ${error.message}`);
    }
  }
}