# Department Management API Documentation

## Overview
The Department Management API allows CompanyAdmin users to create, update, and delete departments within their company, while any authenticated user can read department information.

## Authentication
All endpoints require JWT authentication. Include the access token in the Authorization header:
```
Authorization: Bearer YOUR_ACCESS_TOKEN
```

## Authorization Levels
- **Read Operations**: Any authenticated user (CompanyAdmin, Supervisor, Employee)
- **Write Operations**: Only CompanyAdmin role

## Endpoints

### 1. Create Department
**POST** `/api/departments`

**Authorization**: CompanyAdmin only

**Request Body**:
```json
{
  "name": "Engineering",
  "description": "Software development and engineering team"
}
```

**Response**:
```json
{
  "id": "123e4567-e89b-12d3-a456-************",
  "name": "Engineering",
  "description": "Software development and engineering team",
  "company_id": "987fcdeb-51a2-43d1-9c4e-123456789abc",
  "isActive": true,
  "createdAt": "2024-01-15T10:30:00.000Z",
  "updatedAt": "2024-01-15T10:30:00.000Z",
  "createdBy": "user-uuid"
}
```

**cURL Example**:
```bash
curl -X POST http://localhost:3000/api/departments \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -d '{
    "name": "Engineering",
    "description": "Software development and engineering team"
  }'
```

### 2. Update Department
**PUT** `/api/departments/:id`

**Authorization**: CompanyAdmin only

**Request Body**:
```json
{
  "name": "Software Engineering",
  "description": "Updated description",
  "isActive": true
}
```

**Response**: Same as create department response

**cURL Example**:
```bash
curl -X PUT http://localhost:3000/api/departments/123e4567-e89b-12d3-a456-************ \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -d '{
    "name": "Software Engineering",
    "description": "Updated description"
  }'
```

### 3. Get All Departments
**GET** `/api/departments`

**Authorization**: Any authenticated user

**Response**:
```json
[
  {
    "id": "123e4567-e89b-12d3-a456-************",
    "name": "Engineering",
    "description": "Software development team",
    "company_id": "987fcdeb-51a2-43d1-9c4e-123456789abc",
    "isActive": true,
    "createdAt": "2024-01-15T10:30:00.000Z",
    "updatedAt": "2024-01-15T10:30:00.000Z",
    "createdBy": "user-uuid"
  },
  {
    "id": "456e7890-e89b-12d3-a456-************",
    "name": "Marketing",
    "description": "Marketing and sales team",
    "company_id": "987fcdeb-51a2-43d1-9c4e-123456789abc",
    "isActive": true,
    "createdAt": "2024-01-15T11:00:00.000Z",
    "updatedAt": "2024-01-15T11:00:00.000Z",
    "createdBy": "user-uuid"
  }
]
```

**cURL Example**:
```bash
curl -X GET http://localhost:3000/api/departments \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 4. Get Department by ID
**GET** `/api/departments/:id`

**Authorization**: Any authenticated user

**Response**: Same as single department object from get all departments

**cURL Example**:
```bash
curl -X GET http://localhost:3000/api/departments/123e4567-e89b-12d3-a456-************ \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 5. Delete Department
**DELETE** `/api/departments/:id`

**Authorization**: CompanyAdmin only

**Response**:
```json
{
  "message": "Department deleted successfully"
}
```

**cURL Example**:
```bash
curl -X DELETE http://localhost:3000/api/departments/123e4567-e89b-12d3-a456-************ \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

## Error Responses

### 400 Bad Request
```json
{
  "statusCode": 400,
  "message": "Department with name 'Engineering' already exists in this company",
  "error": "Bad Request"
}
```

### 401 Unauthorized
```json
{
  "statusCode": 401,
  "message": "Unauthorized",
  "error": "Unauthorized"
}
```

### 403 Forbidden
```json
{
  "statusCode": 403,
  "message": "You can only update departments within your company",
  "error": "Forbidden"
}
```

### 404 Not Found
```json
{
  "statusCode": 404,
  "message": "Department not found",
  "error": "Not Found"
}
```

## Validation Rules
- **name**: Required, string, max 150 characters, must be unique within company
- **description**: Optional, string, max 500 characters
- **isActive**: Optional, boolean (for updates only)

## Security Features
- JWT authentication required for all endpoints
- Role-based authorization (CompanyAdmin for write operations)
- Company isolation (users can only access departments within their company)
- Input validation and sanitization
- Soft delete (departments are marked as deleted, not physically removed)

## Database Migration
To create the departments table, run:
```bash
npx sequelize-cli db:migrate
```

The migration file is located at: `src/database/migrations/create-departments-table.js`
