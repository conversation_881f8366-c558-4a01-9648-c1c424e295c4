'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('Reports', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.literal('(NEWID())'),
        primaryKey: true,
        allowNull: false
      },
      name: {
        type: Sequelize.STRING(100),
        allowNull: false
      },
      type: {
        type: Sequelize.ENUM(
          'company_survey_analytics',
          'supervisor_team_analytics', 
          'question_category_breakdown',
          'trend_analysis',
          'department_analytics',
          'export_data'
        ),
        allowNull: false,
        comment: 'Type of report generated'
      },
      status: {
        type: Sequelize.ENUM('pending', 'processing', 'completed', 'failed'),
        allowNull: false,
        defaultValue: 'pending',
        comment: 'Current status of the report'
      },
      format: {
        type: Sequelize.ENUM('json', 'csv', 'pdf', 'excel'),
        allowNull: false,
        defaultValue: 'json',
        comment: 'Format of the report output'
      },
      company_id: {
        type: Sequelize.UUID,
        allowNull: false,
        comment: 'Company this report belongs to',
        references: {
          model: 'Companies',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      requested_by: {
        type: Sequelize.UUID,
        allowNull: false,
        comment: 'User who requested the report',
        references: {
          model: 'Users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      start_date: {
        type: Sequelize.DATE,
        allowNull: false,
        comment: 'Start date for report data range'
      },
      end_date: {
        type: Sequelize.DATE,
        allowNull: false,
        comment: 'End date for report data range'
      },
      parameters: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: 'Additional parameters for report generation (JSON string)'
      },
      data: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: 'Generated report data (JSON string)'
      },
      file_path: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: 'File path if report is saved as file'
      },
      file_size: {
        type: Sequelize.INTEGER,
        allowNull: true,
        comment: 'File size in bytes'
      },
      processing_started_at: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: 'When the report generation started'
      },
      processing_completed_at: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: 'When the report generation completed'
      },
      error_message: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: 'Error message if report generation failed'
      },
      expires_at: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: 'When the report expires and can be deleted'
      },
      isActive: {
        type: Sequelize.BOOLEAN,
        defaultValue: true,
        comment: 'Whether the report is active'
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      },
      createdBy: {
        type: Sequelize.UUID,
        allowNull: true
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      },
      updatedBy: {
        type: Sequelize.UUID,
        allowNull: true
      },
      isDeleted: {
        type: Sequelize.BOOLEAN,
        defaultValue: false
      },
      deletedBy: {
        type: Sequelize.UUID,
        allowNull: true
      }
    });

    // Add indexes for better performance
    await queryInterface.addIndex('Reports', ['company_id']);
    await queryInterface.addIndex('Reports', ['requested_by']);
    await queryInterface.addIndex('Reports', ['type']);
    await queryInterface.addIndex('Reports', ['status']);
    await queryInterface.addIndex('Reports', ['start_date', 'end_date']);
    await queryInterface.addIndex('Reports', ['expires_at']);
    await queryInterface.addIndex('Reports', ['isDeleted']);
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('Reports');
  }
};
